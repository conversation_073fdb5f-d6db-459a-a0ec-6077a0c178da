<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\UniteEnseignement;

echo "=== COMPARING UE SPECIALITIES WITH USERCONTROLLER SPECIALITES ARRAY ===\n\n";

// UserController specialites array (from the code)
$userControllerSpecialites = [
    'Structures et béton armé',
    'Géotechnique',
    'Hydraulique urbaine',
    'Topographie',
    'Matériaux de construction',
    'Modélisation et calcul de structures',
    'Machines électriques',
    'Électronique de puissance',
    'Automatismes',
    'Réseaux électriques',
    'Commande des systèmes',
    'Développement logiciel',
    'Systèmes d\'exploitation',
    'Sécurité informatique',
    'Intelligence artificielle',
    'Réseaux & cybersécurité',
    'Bases de données',
    'CAO/DAO',
    'Mécanique des solides',
    'Fabrication mécanique',
    'Tribologie',
    'Vibrations et acoustique',
    'Thermodynamique',
    'Transferts thermiques',
    'Systèmes énergétiques',
    'Energies renouvelables',
    'Efficacité énergétique',
    'Traitement des eaux',
    'Hydrologie',
    'Écologie industrielle',
    'Analyse du cycle de vie',
    'Génie des procédés environnementaux',
    'Chimie organique et analytique',
    'Thermochimie',
    'Génie des réacteurs',
    'Opérations unitaires',
    'Séparation et distillation',
    'Réseaux informatiques',
    'Télécommunications',
    'Systèmes embarqués',
    'Protocoles réseau',
    'Analyse / Algèbre',
    'Statistiques et probabilités',
    'Mécanique physique',
    'Thermodynamique fondamentale',
    'Communication écrite et orale',
    'Anglais technique',
    'Français scientifique',
    'Management de projet',
    'Entrepreneuriat / Innovation'
];

echo "UserController specialites array contains: " . count($userControllerSpecialites) . " specialities\n\n";

// Get all UE specialities from database
$ues = UniteEnseignement::whereNotNull('specialite')
    ->where('specialite', '!=', '')
    ->with(['filiere', 'departement'])
    ->get();

// Parse all UE specialities
$allUeSpecialities = [];
$ueSpecialitiesDetails = [];

foreach ($ues as $ue) {
    if ($ue->specialite) {
        $specialities = array_map('trim', explode(',', $ue->specialite));
        $specialities = array_filter($specialities);
        
        foreach ($specialities as $spec) {
            if (!in_array($spec, $allUeSpecialities)) {
                $allUeSpecialities[] = $spec;
            }
            
            if (!isset($ueSpecialitiesDetails[$spec])) {
                $ueSpecialitiesDetails[$spec] = [];
            }
            $ueSpecialitiesDetails[$spec][] = [
                'ue_code' => $ue->code,
                'ue_nom' => $ue->nom,
                'filiere' => $ue->filiere->nom ?? 'Unknown',
                'est_vacant' => $ue->est_vacant,
                'vacataire_types' => $ue->vacataire_types
            ];
        }
    }
}

sort($allUeSpecialities);

echo "Database UE specialities found: " . count($allUeSpecialities) . " unique specialities\n\n";

// Compare arrays
$matchingSpecialities = [];
$ueOnlySpecialities = [];
$userControllerOnlySpecialities = [];

// Check UE specialities against UserController array
foreach ($allUeSpecialities as $ueSpec) {
    $found = false;
    foreach ($userControllerSpecialites as $userSpec) {
        if (strcasecmp($ueSpec, $userSpec) === 0) {
            $matchingSpecialities[] = $ueSpec;
            $found = true;
            break;
        }
    }
    if (!$found) {
        $ueOnlySpecialities[] = $ueSpec;
    }
}

// Check UserController specialities not in UEs
foreach ($userControllerSpecialites as $userSpec) {
    $found = false;
    foreach ($allUeSpecialities as $ueSpec) {
        if (strcasecmp($userSpec, $ueSpec) === 0) {
            $found = true;
            break;
        }
    }
    if (!$found) {
        $userControllerOnlySpecialities[] = $userSpec;
    }
}

echo "=== COMPARISON RESULTS ===\n\n";

echo "✅ MATCHING SPECIALITIES (" . count($matchingSpecialities) . "):\n";
foreach ($matchingSpecialities as $i => $spec) {
    echo ($i + 1) . ". {$spec}\n";
}

echo "\n❌ UE-ONLY SPECIALITIES (NOT in UserController array) (" . count($ueOnlySpecialities) . "):\n";
foreach ($ueOnlySpecialities as $i => $spec) {
    echo ($i + 1) . ". {$spec}\n";
    echo "   Used in UEs:\n";
    foreach ($ueSpecialitiesDetails[$spec] as $detail) {
        $vacantStatus = $detail['est_vacant'] ? 'VACANT' : 'NOT VACANT';
        $vacataireTypes = $detail['vacataire_types'] ? 
            (is_string($detail['vacataire_types']) ? $detail['vacataire_types'] : json_encode($detail['vacataire_types'])) : 
            'None';
        echo "     - {$detail['ue_code']} ({$detail['ue_nom']}) - {$detail['filiere']} - {$vacantStatus} - Vacataire: {$vacataireTypes}\n";
    }
    echo "\n";
}

echo "\n⚠️  USERCONTROLLER-ONLY SPECIALITIES (NOT used in any UE) (" . count($userControllerOnlySpecialities) . "):\n";
foreach ($userControllerOnlySpecialities as $i => $spec) {
    echo ($i + 1) . ". {$spec}\n";
}

echo "\n=== DETAILED ANALYSIS ===\n\n";

echo "Total UserController specialities: " . count($userControllerSpecialites) . "\n";
echo "Total UE specialities: " . count($allUeSpecialities) . "\n";
echo "Matching specialities: " . count($matchingSpecialities) . "\n";
echo "UE-only specialities: " . count($ueOnlySpecialities) . "\n";
echo "UserController-only specialities: " . count($userControllerOnlySpecialities) . "\n";

$matchPercentage = count($allUeSpecialities) > 0 ? 
    round((count($matchingSpecialities) / count($allUeSpecialities)) * 100, 2) : 0;

echo "Match percentage: {$matchPercentage}%\n";

echo "\n=== RECOMMENDATIONS ===\n\n";

if (count($ueOnlySpecialities) > 0) {
    echo "🔧 ADD TO USERCONTROLLER ARRAY:\n";
    echo "Add these specialities to the UserController \$specialites array:\n\n";
    foreach ($ueOnlySpecialities as $spec) {
        echo "'{$spec}',\n";
    }
    echo "\n";
}

if (count($userControllerOnlySpecialities) > 0) {
    echo "📝 UNUSED USERCONTROLLER SPECIALITIES:\n";
    echo "These specialities are defined in UserController but not used in any UE:\n";
    echo "Consider creating UEs with these specialities or removing them if not needed:\n\n";
    foreach ($userControllerOnlySpecialities as $spec) {
        echo "- {$spec}\n";
    }
    echo "\n";
}

echo "=== CRITICAL UE SPECIALITIES FOR VACATAIRE ASSIGNMENT ===\n\n";

// Focus on UEs that are vacant or have vacataire_types
$criticalUeSpecs = [];
foreach ($ueSpecialitiesDetails as $spec => $details) {
    $hasCriticalUe = false;
    foreach ($details as $detail) {
        if ($detail['est_vacant'] || $detail['vacataire_types']) {
            $hasCriticalUe = true;
            break;
        }
    }
    if ($hasCriticalUe && in_array($spec, $ueOnlySpecialities)) {
        $criticalUeSpecs[] = $spec;
    }
}

if (count($criticalUeSpecs) > 0) {
    echo "🚨 PRIORITY ADDITIONS NEEDED:\n";
    echo "These UE specialities are used in VACANT UEs or UEs with vacataire_types\n";
    echo "but are NOT in UserController array (affects vacataire assignment):\n\n";
    foreach ($criticalUeSpecs as $spec) {
        echo "- {$spec}\n";
        foreach ($ueSpecialitiesDetails[$spec] as $detail) {
            if ($detail['est_vacant'] || $detail['vacataire_types']) {
                $status = [];
                if ($detail['est_vacant']) $status[] = 'VACANT';
                if ($detail['vacataire_types']) $status[] = 'HAS_VACATAIRE_TYPES';
                echo "  → {$detail['ue_code']} ({$detail['filiere']}) - " . implode(', ', $status) . "\n";
            }
        }
        echo "\n";
    }
}

echo "=== ANALYSIS COMPLETE ===\n";
