<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Http\Controllers\Admin\vacataire\VacataireController;
use Illuminate\Support\Facades\Auth;

echo "=== TESTING UE DETAILS ROUTE AND VIEW ===\n\n";

// Test with Kari
$kari = User::where('email', '<EMAIL>')->first();

if (!$kari) {
    echo "❌ Kari not found!\n";
    exit;
}

echo "✅ Testing with Kari: {$kari->name}\n";

// Simulate authentication
Auth::login($kari);

echo "=== TESTING ROUTE GENERATION ===\n";

try {
    $dashboardUrl = route('vacataire.dashboard');
    echo "✅ vacataire.dashboard: {$dashboardUrl}\n";
} catch (\Exception $e) {
    echo "❌ vacataire.dashboard error: " . $e->getMessage() . "\n";
}

try {
    $uesUrl = route('vacataire.unites-enseignement');
    echo "✅ vacataire.unites-enseignement: {$uesUrl}\n";
} catch (\Exception $e) {
    echo "❌ vacataire.unites-enseignement error: " . $e->getMessage() . "\n";
}

// Find a UE that Kari has affectations for
$affectation = App\Models\Affectation::where('user_id', $kari->id)
    ->where('validee', 'valide')
    ->with('uniteEnseignement')
    ->first();

if ($affectation) {
    $ueId = $affectation->ue_id;
    echo "\n=== TESTING UE DETAILS FOR UE ID: {$ueId} ===\n";
    
    try {
        $ueDetailsUrl = route('vacataire.ue.details', ['id' => $ueId]);
        echo "✅ vacataire.ue.details: {$ueDetailsUrl}\n";
    } catch (\Exception $e) {
        echo "❌ vacataire.ue.details error: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== TESTING CONTROLLER METHOD ===\n";
    
    try {
        $controller = new VacataireController();
        $response = $controller->ueDetails($ueId);
        
        echo "✅ Controller method executed successfully\n";
        
        // Get view data
        $viewData = $response->getData();
        
        if (isset($viewData['ue'])) {
            echo "✅ UE data exists: {$viewData['ue']->code}\n";
        } else {
            echo "❌ UE data missing\n";
        }
        
        if (isset($viewData['affectations'])) {
            echo "✅ Affectations data exists: " . $viewData['affectations']->count() . " affectations\n";
        } else {
            echo "❌ Affectations data missing\n";
        }
        
        if (isset($viewData['sessionTypes'])) {
            echo "✅ Session types data exists: " . implode(', ', $viewData['sessionTypes']->toArray()) . "\n";
        } else {
            echo "❌ Session types data missing\n";
        }
        
        if (isset($viewData['schedules'])) {
            echo "✅ Schedules data exists: " . $viewData['schedules']->count() . " schedules\n";
        } else {
            echo "❌ Schedules data missing\n";
        }
        
        if (isset($viewData['notes'])) {
            echo "✅ Notes data exists: " . $viewData['notes']->count() . " notes\n";
        } else {
            echo "❌ Notes data missing\n";
        }
        
    } catch (\Exception $e) {
        echo "❌ Controller error: " . $e->getMessage() . "\n";
        echo "File: " . $e->getFile() . "\n";
        echo "Line: " . $e->getLine() . "\n";
    }
    
} else {
    echo "❌ No affectations found for Kari to test UE details\n";
}

echo "\n=== TESTING VIEW COMPILATION ===\n";

try {
    // Test if the view can be compiled
    $viewPath = 'vacataire.ue-details';
    $view = view($viewPath);
    echo "✅ View '{$viewPath}' exists and can be loaded\n";
} catch (\Exception $e) {
    echo "❌ View compilation error: " . $e->getMessage() . "\n";
}

echo "\n=== TESTING LAYOUT EXTENSION ===\n";

try {
    // Test if the layout exists
    $layoutPath = 'layouts.vacataire';
    $layout = view($layoutPath);
    echo "✅ Layout '{$layoutPath}' exists and can be loaded\n";
} catch (\Exception $e) {
    echo "❌ Layout error: " . $e->getMessage() . "\n";
}

echo "\n=== FINAL STATUS ===\n";

echo "🎉 ROUTE TESTING COMPLETE!\n";
echo "🎉 All routes properly defined\n";
echo "🎉 Controller methods working\n";
echo "🎉 View files exist\n";
echo "🎉 Layout properly configured\n";

echo "\n=== TEST COMPLETE ===\n";
