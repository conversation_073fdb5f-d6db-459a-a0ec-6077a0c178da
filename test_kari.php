<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\UniteEnseignement;
use App\Models\Affectation;
use Illuminate\Support\Facades\DB;

echo "=== TESTING KARI USER FOR VACATAIRE ASSIGNMENT ===\n\n";

// Find kari user
$kari = User::where('email', '<EMAIL>')->first();

if (!$kari) {
    echo "❌ User with email '<EMAIL>' not found!\n";
    echo "Let me check for similar users...\n\n";
    
    $users = User::where('email', 'like', '%bta%')
                 ->orWhere('email', 'like', '%kari%')
                 ->orWhere('name', 'like', '%kari%')
                 ->get();
    
    if ($users->count() > 0) {
        echo "Found similar users:\n";
        foreach ($users as $user) {
            echo "- ID: {$user->id}, Name: {$user->name}, Email: {$user->email}, Role: {$user->role}\n";
        }
    } else {
        echo "No similar users found.\n";
        echo "Let me show all vacataire users:\n\n";
        
        $vacataires = User::where('role', 'vacataire')->get();
        foreach ($vacataires as $vac) {
            echo "- ID: {$vac->id}, Name: {$vac->name}, Email: {$vac->email}, Specialite: {$vac->specialite}\n";
        }
    }
    exit;
}

echo "✅ Found user: {$kari->name}\n";
echo "- ID: {$kari->id}\n";
echo "- Email: {$kari->email}\n";
echo "- Role: {$kari->role}\n";
echo "- Specialite: " . ($kari->specialite ?? 'None') . "\n";
echo "- Departement ID: {$kari->departement_id}\n\n";

// Get current year
$currentYear = date('Y') . '-' . (date('Y') + 1);
echo "Current academic year: {$currentYear}\n\n";

// Check kari's existing affectations
$affectations = Affectation::where('user_id', $kari->id)
    ->where('annee_universitaire', $currentYear)
    ->where('validee', 'valide')
    ->with('uniteEnseignement')
    ->get();

echo "=== KARI'S EXISTING AFFECTATIONS ===\n";
if ($affectations->count() > 0) {
    foreach ($affectations as $aff) {
        echo "- UE: {$aff->uniteEnseignement->code} ({$aff->uniteEnseignement->nom})\n";
        echo "  Type: {$aff->type_seance}\n";
        echo "  Status: {$aff->validee}\n\n";
    }
} else {
    echo "No existing affectations found.\n\n";
}

// Now test the filtering logic
echo "=== TESTING FILTERING LOGIC ===\n\n";

// Simulate coordonnateur (assume GI coordonnateur)
$coordonnateur = User::where('role', 'coordonnateur')->first();
if (!$coordonnateur) {
    echo "❌ No coordonnateur found!\n";
    exit;
}

echo "Testing with coordonnateur: {$coordonnateur->name}\n";

// Get coordonnateur's managed filieres
$filiereIds = DB::table('coordonnateurs_filieres')
    ->where('user_id', $coordonnateur->id)
    ->pluck('filiere_id');

echo "Coordonnateur manages filieres: " . $filiereIds->implode(', ') . "\n\n";

// Apply the filtering logic
echo "=== APPLYING FILTERING CONDITIONS ===\n\n";

// Get kari's specialities
$kariSpecialites = [];
if ($kari->specialite) {
    $kariSpecialites = array_map('trim', explode(',', $kari->specialite));
    $kariSpecialites = array_filter($kariSpecialites);
}
echo "Kari's specialities: " . implode(', ', $kariSpecialites) . "\n\n";

// Get candidate UEs
$ues = UniteEnseignement::whereIn('filiere_id', $filiereIds)
    ->where('est_vacant', true)
    ->whereNotNull('vacataire_types')
    ->where('vacataire_types', '!=', '[]')
    ->where('vacataire_types', '!=', 'null')
    ->whereDoesntHave('affectations', function($query) use ($currentYear) {
        $query->where('annee_universitaire', $currentYear)
              ->where('validee', 'valide');
    })
    ->with(['filiere', 'departement'])
    ->get();

echo "Found {$ues->count()} candidate UEs (vacant + no affectations + has vacataire_types)\n\n";

$compatibleUEs = [];

foreach ($ues as $ue) {
    echo "--- Testing UE: {$ue->code} ({$ue->nom}) ---\n";
    echo "Filiere: {$ue->filiere->nom}\n";
    echo "Specialite: " . ($ue->specialite ?? 'None') . "\n";
    $vacataireTypesDisplay = 'None';
    if ($ue->vacataire_types) {
        if (is_string($ue->vacataire_types)) {
            $decoded = json_decode($ue->vacataire_types, true);
            if (is_array($decoded)) {
                $vacataireTypesDisplay = implode(', ', $decoded);
            } else {
                $vacataireTypesDisplay = $ue->vacataire_types;
            }
        } else if (is_array($ue->vacataire_types)) {
            $vacataireTypesDisplay = implode(', ', $ue->vacataire_types);
        }
    }
    echo "Vacataire types: {$vacataireTypesDisplay}\n";
    
    // Test speciality compatibility
    $isSpecialityCompatible = false;
    
    if (empty($ue->specialite)) {
        $isSpecialityCompatible = true;
        echo "✅ Speciality: Compatible (UE has no speciality requirement)\n";
    } else if (empty($kariSpecialites)) {
        $isSpecialityCompatible = false;
        echo "❌ Speciality: Incompatible (Kari has no specialities, UE requires: {$ue->specialite})\n";
    } else {
        $ueSpecialites = array_map('trim', explode(',', $ue->specialite));
        $ueSpecialites = array_filter($ueSpecialites);
        
        $isSpecialityCompatible = true;
        foreach ($ueSpecialites as $ueSpec) {
            $ueSpecCovered = false;
            foreach ($kariSpecialites as $kariSpec) {
                if (stripos($ueSpec, $kariSpec) !== false || stripos($kariSpec, $ueSpec) !== false) {
                    $ueSpecCovered = true;
                    break;
                }
            }
            if (!$ueSpecCovered) {
                $isSpecialityCompatible = false;
                echo "❌ Speciality: Incompatible (UE requires '{$ueSpec}' not covered by Kari)\n";
                break;
            }
        }
        if ($isSpecialityCompatible) {
            echo "✅ Speciality: Compatible (All UE specialities covered by Kari)\n";
        }
    }
    
    if (!$isSpecialityCompatible) {
        echo "❌ UE REJECTED: Speciality mismatch\n\n";
        continue;
    }
    
    // Test vacataire types
    $vacataireTypes = [];
    if ($ue->vacataire_types) {
        $decoded = json_decode($ue->vacataire_types, true);
        if (is_array($decoded)) {
            $vacataireTypes = $decoded;
        }
    }
    
    if (empty($vacataireTypes)) {
        echo "❌ UE REJECTED: No vacataire types defined\n\n";
        continue;
    }
    
    echo "Vacataire types available: " . implode(', ', $vacataireTypes) . "\n";
    
    // Check session types not assigned to any user
    $assignedTypesToAnyUser = Affectation::where('ue_id', $ue->id)
        ->where('annee_universitaire', $currentYear)
        ->where('validee', 'valide')
        ->distinct()
        ->pluck('type_seance')
        ->toArray();
    
    echo "Types assigned to any user: " . (empty($assignedTypesToAnyUser) ? 'None' : implode(', ', $assignedTypesToAnyUser)) . "\n";
    
    $availableTypes = [];
    foreach ($vacataireTypes as $type) {
        if (!in_array($type, $assignedTypesToAnyUser)) {
            $availableTypes[] = $type;
        }
    }
    
    if (empty($availableTypes)) {
        echo "❌ UE REJECTED: No available session types\n\n";
        continue;
    }
    
    echo "✅ Available types for assignment: " . implode(', ', $availableTypes) . "\n";
    echo "✅ UE ACCEPTED for assignment\n\n";
    
    $compatibleUEs[] = [
        'id' => $ue->id,
        'code' => $ue->code,
        'nom' => $ue->nom,
        'specialite' => $ue->specialite,
        'filiere_nom' => $ue->filiere->nom,
        'departement_nom' => $ue->departement->nom,
        'vacataire_types' => $availableTypes,
        'total_hours' => $ue->heures_cm + $ue->heures_td + $ue->heures_tp
    ];
}

echo "=== FINAL RESULT ===\n";
echo "Compatible UEs for Kari: " . count($compatibleUEs) . "\n\n";

if (count($compatibleUEs) > 0) {
    foreach ($compatibleUEs as $ue) {
        echo "✅ {$ue['code']} - {$ue['nom']}\n";
        echo "   Filiere: {$ue['filiere_nom']}\n";
        echo "   Available types: " . implode(', ', $ue['vacataire_types']) . "\n";
        echo "   Total hours: {$ue['total_hours']}\n\n";
    }
} else {
    echo "❌ No compatible UEs found for Kari.\n";
}

echo "=== TEST COMPLETE ===\n";
