<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\Affectation;

echo "=== CHECKING KARI'S AFFECTATIONS ===\n\n";

$kari = User::where('email', '<EMAIL>')->first();

if ($kari) {
    echo "✅ Kari found: {$kari->name} (ID: {$kari->id})\n";
    echo "Role: {$kari->role}\n\n";
    
    $affectations = Affectation::where('user_id', $kari->id)
        ->where('validee', 'valide')
        ->with('uniteEnseignement')
        ->get();
    
    echo "Kari's affectations: " . $affectations->count() . "\n\n";
    
    if ($affectations->count() > 0) {
        foreach ($affectations as $affectation) {
            echo "- {$affectation->uniteEnseignement->code} ({$affectation->type_seance})\n";
        }
    } else {
        echo "❌ Kari has no valid affectations\n";
        
        // Check if there are any affectations at all
        $allAffectations = Affectation::where('user_id', $kari->id)->get();
        echo "Total affectations (all statuses): " . $allAffectations->count() . "\n";
        
        if ($allAffectations->count() > 0) {
            foreach ($allAffectations as $affectation) {
                echo "- {$affectation->uniteEnseignement->code} ({$affectation->type_seance}) - Status: {$affectation->validee}\n";
            }
        }
    }
} else {
    echo "❌ Kari not found!\n";
}

echo "\n=== CREATING TEST AFFECTATION FOR KARI ===\n";

if ($kari) {
    // Find T121 UE
    $t121 = App\Models\UniteEnseignement::where('code', 'T121')->first();
    
    if ($t121) {
        echo "✅ Found T121: {$t121->nom}\n";
        
        // Check if affectation already exists
        $existingAffectation = Affectation::where('user_id', $kari->id)
            ->where('ue_id', $t121->id)
            ->first();
        
        if (!$existingAffectation) {
            // Create affectation
            $affectation = Affectation::create([
                'user_id' => $kari->id,
                'ue_id' => $t121->id,
                'type_seance' => 'CM',
                'annee_universitaire' => date('Y') . '-' . (date('Y') + 1),
                'validee' => 'valide',
                'created_by' => 1 // Admin
            ]);
            
            echo "✅ Created CM affectation for T121\n";
            
            // Create TP affectation too
            $affectationTP = Affectation::create([
                'user_id' => $kari->id,
                'ue_id' => $t121->id,
                'type_seance' => 'TP',
                'annee_universitaire' => date('Y') . '-' . (date('Y') + 1),
                'validee' => 'valide',
                'created_by' => 1 // Admin
            ]);
            
            echo "✅ Created TP affectation for T121\n";
            
        } else {
            echo "⚠️  Affectation already exists for T121\n";
        }
    } else {
        echo "❌ T121 not found!\n";
    }
}

echo "\n=== FINAL CHECK ===\n";

if ($kari) {
    $finalAffectations = Affectation::where('user_id', $kari->id)
        ->where('validee', 'valide')
        ->with('uniteEnseignement')
        ->get();
    
    echo "Kari's final affectations: " . $finalAffectations->count() . "\n";
    
    foreach ($finalAffectations as $affectation) {
        echo "- {$affectation->uniteEnseignement->code} ({$affectation->type_seance})\n";
    }
}

echo "\n=== COMPLETE ===\n";
