<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Http\Controllers\Admin\vacataire\VacataireController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

echo "=== TESTING VACATAIRE PDF EXPORT FUNCTIONALITY ===\n\n";

// Test with Kari
$kari = User::where('email', '<EMAIL>')->first();

if (!$kari) {
    echo "❌ Kari not found!\n";
    exit;
}

echo "✅ Testing with Kari: {$kari->name}\n";

// Simulate authentication
Auth::login($kari);

echo "=== TESTING ROUTE EXISTENCE ===\n";

try {
    $exportUrl = route('vacataire.emploi-du-temps.export');
    echo "✅ Export route exists: {$exportUrl}\n";
} catch (\Exception $e) {
    echo "❌ Export route error: " . $e->getMessage() . "\n";
}

echo "\n=== TESTING CONTROLLER METHOD ===\n";

try {
    $controller = new VacataireController();
    $request = new Request();
    
    // Test the export method
    $response = $controller->exportEmploiDuTemps($request);
    
    echo "✅ Export method executed successfully\n";
    echo "✅ Response type: " . get_class($response) . "\n";
    
    // Check if it's a download response
    if (method_exists($response, 'headers')) {
        $headers = $response->headers->all();
        if (isset($headers['content-type']) && strpos($headers['content-type'][0], 'pdf') !== false) {
            echo "✅ PDF content type detected\n";
        }
        if (isset($headers['content-disposition'])) {
            echo "✅ Download disposition set\n";
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Export method error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "\n=== TESTING DATA PREPARATION ===\n";

// Test the data that would be used for PDF generation
$vacataire = $kari;
$currentYear = date('Y') . '-' . (date('Y') + 1);

// Get vacataire's valid affectations
$affectations = App\Models\Affectation::where('user_id', $vacataire->id)
    ->where('validee', 'valide')
    ->get();

echo "✅ Vacataire affectations: " . $affectations->count() . "\n";

if ($affectations->count() > 0) {
    // Create a map of UE+type combinations
    $assignedCombinations = $affectations->map(function($affectation) {
        return $affectation->ue_id . '_' . $affectation->type_seance;
    })->toArray();
    
    echo "✅ Assigned combinations: " . implode(', ', $assignedCombinations) . "\n";
    
    // Get all schedules for this vacataire
    $allSchedules = App\Models\Schedule::where('user_id', $vacataire->id)
        ->where('annee_universitaire', $currentYear)
        ->with(['uniteEnseignement'])
        ->get();
    
    echo "✅ Total schedules: " . $allSchedules->count() . "\n";
    
    // Filter schedules to only show assigned session types
    $filteredSchedules = $allSchedules->filter(function($schedule) use ($assignedCombinations) {
        $combination = $schedule->ue_id . '_' . $schedule->type_seance;
        return in_array($combination, $assignedCombinations);
    });
    
    echo "✅ Filtered schedules: " . $filteredSchedules->count() . "\n";
    
    if ($filteredSchedules->count() > 0) {
        echo "\n=== SCHEDULE DETAILS ===\n";
        foreach ($filteredSchedules as $schedule) {
            echo "- {$schedule->uniteEnseignement->code} ({$schedule->type_seance}) - {$schedule->jour_semaine} {$schedule->heure_debut}-{$schedule->heure_fin}\n";
        }
    }
} else {
    echo "⚠️  No affectations found for Kari\n";
}

echo "\n=== TESTING PDF TEMPLATE ===\n";

try {
    // Test if the PDF template exists
    $templatePath = 'vacataire.exports.emploi-du-temps';
    $view = view($templatePath, [
        'vacataire' => $vacataire,
        'schedules' => collect([]), // Empty collection for testing
        'title' => 'Test',
        'currentDate' => date('d/m/Y'),
        'academicYear' => $currentYear
    ]);
    
    echo "✅ PDF template exists and can be loaded\n";
    
} catch (\Exception $e) {
    echo "❌ PDF template error: " . $e->getMessage() . "\n";
}

echo "\n=== TESTING LOGO FILE ===\n";

$logoPath = public_path('images/logo.png');
if (file_exists($logoPath)) {
    echo "✅ Logo file exists: {$logoPath}\n";
    echo "✅ Logo file size: " . filesize($logoPath) . " bytes\n";
} else {
    echo "❌ Logo file missing: {$logoPath}\n";
}

echo "\n=== TESTING DOMPDF LIBRARY ===\n";

try {
    // Test if Dompdf is available
    if (class_exists('Barryvdh\DomPDF\Facade\Pdf')) {
        echo "✅ DomPDF library available\n";
        
        // Test basic PDF generation
        $testPdf = \Barryvdh\DomPDF\Facade\Pdf::loadHTML('<h1>Test PDF</h1>');
        echo "✅ Basic PDF generation works\n";
        
    } else {
        echo "❌ DomPDF library not available\n";
    }
} catch (\Exception $e) {
    echo "❌ DomPDF test error: " . $e->getMessage() . "\n";
}

echo "\n=== FINAL STATUS ===\n";

$issues = [];
$overallStatus = true;

// Check critical components
if (!route('vacataire.emploi-du-temps.export')) {
    $issues[] = "Export route missing";
    $overallStatus = false;
}

if (!file_exists($logoPath)) {
    $issues[] = "Logo file missing";
    $overallStatus = false;
}

if (!class_exists('Barryvdh\DomPDF\Facade\Pdf')) {
    $issues[] = "DomPDF library missing";
    $overallStatus = false;
}

if ($overallStatus) {
    echo "🎉 VACATAIRE PDF EXPORT SYSTEM: ✅ FULLY OPERATIONAL\n";
    echo "🎉 All components working correctly\n";
    echo "🎉 PDF export ready for use\n";
    echo "🎉 Template and data filtering implemented\n";
    echo "🎉 Logo and styling configured\n";
} else {
    echo "⚠️  SYSTEM STATUS: ❌ ISSUES DETECTED\n";
    echo "Issues found:\n";
    foreach ($issues as $issue) {
        echo "   - {$issue}\n";
    }
}

echo "\n=== TEST COMPLETE ===\n";
