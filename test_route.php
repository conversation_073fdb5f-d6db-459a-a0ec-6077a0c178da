<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING VACATAIRE ROUTES ===\n\n";

// Test route generation
try {
    $dashboardUrl = route('vacataire.dashboard');
    echo "✅ vacataire.dashboard route: {$dashboardUrl}\n";
} catch (\Exception $e) {
    echo "❌ vacataire.dashboard route error: " . $e->getMessage() . "\n";
}

try {
    $uesUrl = route('vacataire.unites-enseignement');
    echo "✅ vacataire.unites-enseignement route: {$uesUrl}\n";
} catch (\Exception $e) {
    echo "❌ vacataire.unites-enseignement route error: " . $e->getMessage() . "\n";
}

try {
    $ueDetailsUrl = route('vacataire.ue.details', ['id' => 1]);
    echo "✅ vacataire.ue.details route: {$ueDetailsUrl}\n";
} catch (\Exception $e) {
    echo "❌ vacataire.ue.details route error: " . $e->getMessage() . "\n";
}

echo "\n=== TESTING ROUTE RESOLUTION ===\n";

// Test if routes are properly registered
$router = app('router');
$routes = $router->getRoutes();

$vacataireRoutes = [];
foreach ($routes as $route) {
    $name = $route->getName();
    if ($name && strpos($name, 'vacataire.') === 0) {
        $vacataireRoutes[] = $name;
    }
}

echo "Found " . count($vacataireRoutes) . " vacataire routes:\n";
foreach ($vacataireRoutes as $routeName) {
    echo "- {$routeName}\n";
}

echo "\n=== TESTING SPECIFIC ROUTES ===\n";

$requiredRoutes = [
    'vacataire.dashboard',
    'vacataire.unites-enseignement',
    'vacataire.ue.details'
];

foreach ($requiredRoutes as $routeName) {
    if (in_array($routeName, $vacataireRoutes)) {
        echo "✅ {$routeName} - EXISTS\n";
    } else {
        echo "❌ {$routeName} - MISSING\n";
    }
}

echo "\n=== TEST COMPLETE ===\n";
