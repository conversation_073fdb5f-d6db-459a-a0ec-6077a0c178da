<?php

namespace App\Imports;

use App\Models\Note;
use App\Models\User;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\WithStartRow;

class NotesImport implements ToCollection, WithHeadingRow, WithValidation, WithStartRow
{
    protected $ueId;
    protected $uploadedBy;
    protected $sessionType;
    protected $importedCount = 0;
    protected $errors = [];

    public function __construct($ueId, $uploadedBy, $sessionType)
    {
        $this->ueId = $ueId;
        $this->uploadedBy = $uploadedBy;
        $this->sessionType = $sessionType;
    }

    /**
     * Start reading from row 3 (skip UE info rows)
     */
    public function startRow(): int
    {
        return 3; // Skip UE info rows and start from headers
    }

    public function collection(Collection $rows)
    {
        foreach ($rows as $index => $row) {
            try {
                // Calculate actual row number (accounting for UE info rows and header)
                $actualRowNumber = $index + 4; // 2 UE info rows + 1 header row + current row

                // Convert row to array and normalize keys
                $rowData = $row->toArray();
                $normalizedRow = [];

                // Normalize column names to handle different formats
                foreach ($rowData as $key => $value) {
                    $normalizedKey = strtolower(str_replace([' ', '_'], '', $key));
                    $normalizedRow[$normalizedKey] = $value;
                }

                // Map expected column names
                $nomEtudiant = $normalizedRow['nometudiant'] ?? $normalizedRow['nom'] ?? null;
                $cneEtudiant = $normalizedRow['cneetudiant'] ?? $normalizedRow['cne'] ?? $normalizedRow['matricule'] ?? null;
                $noteValue = $normalizedRow['note'] ?? null;
                $statutAbsence = $normalizedRow['statutabsence'] ?? $normalizedRow['absence'] ?? $normalizedRow['statut'] ?? null;

                // Skip completely empty rows
                if (empty($nomEtudiant) && empty($cneEtudiant) && empty($noteValue) && empty($statutAbsence)) {
                    continue;
                }

                // Skip rows that are clearly not student data (like headers or empty rows)
                if (empty($nomEtudiant) && empty($cneEtudiant)) {
                    continue;
                }

                // Find student by CNE (matricule) first, then by name
                $etudiant = null;

                if (!empty($cneEtudiant)) {
                    $etudiant = User::where('matricule', trim($cneEtudiant))
                        ->where('role', 'etudiant')
                        ->first();
                }

                if (!$etudiant && !empty($nomEtudiant)) {
                    // Try exact match first
                    $etudiant = User::where('name', trim($nomEtudiant))
                        ->where('role', 'etudiant')
                        ->first();

                    // If not found, try partial match
                    if (!$etudiant) {
                        $etudiant = User::where('name', 'LIKE', '%' . trim($nomEtudiant) . '%')
                            ->where('role', 'etudiant')
                            ->first();
                    }
                }

                if (!$etudiant) {
                    $this->errors[] = "Ligne {$actualRowNumber}: Étudiant non trouvé - " . ($nomEtudiant ?? $cneEtudiant ?? 'Données manquantes');
                    continue;
                }

                // Process absence status
                $isAbsent = false;
                if (!empty($statutAbsence)) {
                    $absenceStatus = strtolower(trim($statutAbsence));
                    $isAbsent = in_array($absenceStatus, [
                        'absent', 'abs', 'a', 'absentee', 'absente',
                        '1', 'true', 'oui', 'yes', 'x'
                    ]);
                }

                // Process note value
                $note = null;
                if (!$isAbsent && !empty($noteValue)) {
                    // Clean and normalize note value
                    $cleanNote = str_replace([',', ' '], ['.', ''], trim($noteValue));

                    if (is_numeric($cleanNote)) {
                        $note = (float) $cleanNote;

                        // Validate note range
                        if ($note < 0 || $note > 20) {
                            $this->errors[] = "Ligne {$actualRowNumber}: Note invalide (doit être entre 0 et 20) - {$note}";
                            continue;
                        }
                    } else {
                        $this->errors[] = "Ligne {$actualRowNumber}: Format de note invalide - '{$noteValue}'";
                        continue;
                    }
                }

                // Create or update note in database
                Note::updateOrCreate(
                    [
                        'ue_id' => $this->ueId,
                        'etudiant_id' => $etudiant->id,
                        'session_type' => $this->sessionType
                    ],
                    [
                        'note' => $isAbsent ? null : $note,
                        'is_absent' => $isAbsent,
                        'uploaded_by' => $this->uploadedBy
                    ]
                );

                $this->importedCount++;

            } catch (\Exception $e) {
                $actualRowNumber = $index + 4;
                $this->errors[] = "Ligne {$actualRowNumber}: Erreur système - " . $e->getMessage();
            }
        }
    }

    public function rules(): array
    {
        return [
            'nom_etudiant' => 'nullable|string',
            'cne_etudiant' => 'nullable|string',
            'note' => 'nullable|numeric|min:0|max:20',
            'statut_absence' => 'nullable|string'
        ];
    }

    public function getImportedCount()
    {
        return $this->importedCount;
    }

    public function getErrors()
    {
        return $this->errors;
    }

    public function hasErrors()
    {
        return !empty($this->errors);
    }
}
