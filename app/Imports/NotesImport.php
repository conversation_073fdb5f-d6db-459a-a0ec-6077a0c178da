<?php

namespace App\Imports;

use App\Models\Note;
use App\Models\User;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class NotesImport implements ToCollection, WithHeadingRow, WithValidation
{
    protected $ueId;
    protected $uploadedBy;
    protected $sessionType;
    protected $importedCount = 0;
    protected $errors = [];

    public function __construct($ueId, $uploadedBy, $sessionType)
    {
        $this->ueId = $ueId;
        $this->uploadedBy = $uploadedBy;
        $this->sessionType = $sessionType;
    }

    public function collection(Collection $rows)
    {
        foreach ($rows as $index => $row) {
            try {
                // Skip empty rows
                if (empty($row['nom_etudiant']) && empty($row['cne_etudiant'])) {
                    continue;
                }

                // Find student by CNE (matricule) or name
                $etudiant = null;
                
                if (!empty($row['cne_etudiant'])) {
                    $etudiant = User::where('matricule', $row['cne_etudiant'])
                        ->where('role', 'etudiant')
                        ->first();
                }
                
                if (!$etudiant && !empty($row['nom_etudiant'])) {
                    $etudiant = User::where('name', 'LIKE', '%' . trim($row['nom_etudiant']) . '%')
                        ->where('role', 'etudiant')
                        ->first();
                }

                if (!$etudiant) {
                    $this->errors[] = "Ligne " . ($index + 2) . ": Étudiant non trouvé - " . ($row['nom_etudiant'] ?? $row['cne_etudiant']);
                    continue;
                }

                // Process note and absence status
                $note = null;
                $isAbsent = false;

                // Check absence status
                if (isset($row['statut_absence'])) {
                    $absenceStatus = strtolower(trim($row['statut_absence']));
                    $isAbsent = in_array($absenceStatus, ['absent', 'abs', 'a', '1', 'true', 'oui']);
                }

                // Process note value
                if (!$isAbsent && isset($row['note']) && $row['note'] !== '') {
                    $noteValue = str_replace(',', '.', trim($row['note']));
                    if (is_numeric($noteValue)) {
                        $note = (float) $noteValue;
                        
                        // Validate note range
                        if ($note < 0 || $note > 20) {
                            $this->errors[] = "Ligne " . ($index + 2) . ": Note invalide (doit être entre 0 et 20) - " . $note;
                            continue;
                        }
                    } else {
                        $this->errors[] = "Ligne " . ($index + 2) . ": Format de note invalide - " . $row['note'];
                        continue;
                    }
                }

                // Create or update note
                Note::updateOrCreate(
                    [
                        'ue_id' => $this->ueId,
                        'etudiant_id' => $etudiant->id,
                        'session_type' => $this->sessionType
                    ],
                    [
                        'note' => $isAbsent ? null : $note,
                        'is_absent' => $isAbsent,
                        'uploaded_by' => $this->uploadedBy
                    ]
                );

                $this->importedCount++;

            } catch (\Exception $e) {
                $this->errors[] = "Ligne " . ($index + 2) . ": Erreur - " . $e->getMessage();
            }
        }
    }

    public function rules(): array
    {
        return [
            'nom_etudiant' => 'nullable|string',
            'cne_etudiant' => 'nullable|string',
            'note' => 'nullable|numeric|min:0|max:20',
            'statut_absence' => 'nullable|string'
        ];
    }

    public function getImportedCount()
    {
        return $this->importedCount;
    }

    public function getErrors()
    {
        return $this->errors;
    }

    public function hasErrors()
    {
        return !empty($this->errors);
    }
}
