<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Historique Affectations & Demandes - {{ $year }}</title>
    <style>
        body { font-family: 'Deja<PERSON>u Sans', <PERSON><PERSON>, sans-serif; color: #222; margin: 0; padding: 0; }
        .header { background: #6366f1; color: #fff; padding: 24px 0 16px 0; text-align: center; }
        .header h1 { margin: 0; font-size: 2.2em; letter-spacing: 1px; }
        .header p { margin: 8px 0 0 0; font-size: 1.1em; }
        .section-title { color: #6366f1; font-size: 1.3em; margin-top: 32px; margin-bottom: 12px; border-bottom: 2px solid #6366f1; padding-bottom: 4px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 24px; }
        th, td { border: 1px solid #e5e7eb; padding: 8px 10px; font-size: 0.98em; }
        th { background: #ede9fe; color: #4b2997; font-weight: bold; }
        tr:nth-child(even) { background: #f8fafc; }
        .badge { display: inline-block; padding: 2px 10px; border-radius: 12px; font-size: 0.95em; color: #fff; }
        .badge.valide { background: #059669; }
        .badge.rejete { background: #ef4444; }
        .badge.annule { background: #6b7280; }
        .badge.en_attente { background: #f59e0b; }
        .bar-section { width: 80%; margin: 60px auto 0 auto; }
        .bar-label { font-weight: bold; margin-bottom: 6px; }
        .bar-bg { border-radius: 8px; height: 38px; width: 600px; background: #f3f4f6; }
        .bar-valide { background: #059669; }
        .bar-rejete { background: #ef4444; }
        .bar-en_attente { background: #f59e0b; }
        .bar-annule { background: #6b7280; }
        .bar-inner { height: 38px; border-radius: 8px; display: flex; align-items: center; font-size: 1.3em; color: #fff; font-weight: bold; padding-left: 18px; }
        .page-break { page-break-before: always; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Historique Affectations & Demandes</h1>
        <p>Année universitaire : <strong>{{ $year }}</strong></p>
    </div>

    <div class="section-title">Affectations Validées</div>
    <table>
        <thead>
            <tr>
                <th>UE</th>
                <th>Enseignant</th>
                <th>Type</th>
                <th>Date</th>
            </tr>
        </thead>
        <tbody>
        @forelse($affectations as $aff)
            <tr>
                <td>{{ $aff->uniteEnseignement->code ?? '' }} - {{ $aff->uniteEnseignement->nom ?? '' }}</td>
                <td>{{ $aff->user->name ?? '' }}</td>
                <td>{{ $aff->type_seance }}</td>
                <td>{{ $aff->created_at ? $aff->created_at->format('d/m/Y H:i') : '' }}</td>
            </tr>
        @empty
            <tr><td colspan="4" style="text-align:center; color:#888;">Aucune affectation validée pour cette année.</td></tr>
        @endforelse
        </tbody>
    </table>

    <div class="page-break"></div>
    <div class="section-title">Demandes d'Affectation</div>
    <table>
        <thead>
            <tr>
                <th>UE</th>
                <th>Enseignant</th>
                <th>Type</th>
                <th>Statut</th>
                <th>Date</th>
            </tr>
        </thead>
        <tbody>
        @forelse($demandes as $demande)
            <tr>
                <td>{{ $demande->uniteEnseignement->code ?? '' }} - {{ $demande->uniteEnseignement->nom ?? '' }}</td>
                <td>{{ $demande->user->name ?? '' }}</td>
                <td>{{ $demande->type_seance }}</td>
                <td><span class="badge {{ $demande->validee }}">{{ $demande->validee }}</span></td>
                <td>{{ $demande->created_at ? $demande->created_at->format('d/m/Y H:i') : '' }}</td>
            </tr>
        @empty
            <tr><td colspan="5" style="text-align:center; color:#888;">Aucune demande pour cette année.</td></tr>
        @endforelse
        </tbody>
    </table>

    <div class="page-break"></div>
    <div class="section-title">Diagramme des Statuts des Demandes</div>
    <div class="bar-section">
        @php
            $max = max(1, $stats['valide'], $stats['rejete'], $stats['en_attente'], $stats['annule']);
            $barMaxWidth = 600;
        @endphp
        <div style="margin-bottom: 32px;">
            <div class="bar-label" style="color: #059669;">Validées</div>
            <div class="bar-bg">
                <div class="bar-inner bar-valide" style="width: {{ round($stats['valide']/$max*$barMaxWidth) }}px;">{{ $stats['valide'] }}</div>
            </div>
        </div>
        <div style="margin-bottom: 32px;">
            <div class="bar-label" style="color: #ef4444;">Rejetées</div>
            <div class="bar-bg">
                <div class="bar-inner bar-rejete" style="width: {{ round($stats['rejete']/$max*$barMaxWidth) }}px;">{{ $stats['rejete'] }}</div>
            </div>
        </div>
        <div style="margin-bottom: 32px;">
            <div class="bar-label" style="color: #f59e0b;">En Attente</div>
            <div class="bar-bg">
                <div class="bar-inner bar-en_attente" style="width: {{ round($stats['en_attente']/$max*$barMaxWidth) }}px;">{{ $stats['en_attente'] }}</div>
            </div>
        </div>
        <div style="margin-bottom: 32px;">
            <div class="bar-label" style="color: #6b7280;">Annulées</div>
            <div class="bar-bg">
                <div class="bar-inner bar-annule" style="width: {{ round($stats['annule']/$max*$barMaxWidth) }}px;">{{ $stats['annule'] }}</div>
            </div>
        </div>
    </div>
</body>
</html> 