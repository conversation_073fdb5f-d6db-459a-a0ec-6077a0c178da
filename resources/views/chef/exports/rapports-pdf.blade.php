<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport Statistiques - {{ $selectedYear }}</title>
    <style>
        @page {
            margin: 15mm;
            size: A4;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 11px;
            line-height: 1.4;
            color: #333;
            background: white;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #2c3e50;
        }
        
        .header-left {
            flex: 1;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }
        
        .logo-left {
            width: 50px;
            height: 50px;
            flex-shrink: 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .school-info {
            flex: 1;
        }
        
        .school-name {
            font-size: 13px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 3px;
        }
        
        .school-subtitle {
            font-size: 10px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .current-date {
            font-size: 9px;
            color: #888;
        }
        
        .header-center {
            flex: 2;
            text-align: center;
        }
        
        .document-title {
            font-size: 22px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .year-info {
            font-size: 16px;
            font-weight: 600;
            color: #666;
            margin-bottom: 5px;
        }
        
        .subtitle {
            font-size: 11px;
            color: #888;
        }
        
        .header-right {
            flex: 1;
            text-align: right;
        }
        
        .export-info {
            font-size: 9px;
            color: #888;
        }
        
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #2c3e50;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 16px;
            color: white;
        }
        
        .stat-icon.primary {
            background: linear-gradient(135deg, #2c3e50, #34495e);
        }
        
        .stat-icon.success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }
        
        .stat-icon.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 9px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }
        
        .section {
            margin-bottom: 25px;
            break-inside: avoid;
        }
        
        .section-header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 12px 15px;
            border-radius: 8px 8px 0 0;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 0;
        }
        
        .section-content {
            background: white;
            border: 2px solid #2c3e50;
            border-top: none;
            border-radius: 0 0 8px 8px;
            padding: 15px;
        }
        
        .filiere-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .filiere-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid #2c3e50;
            border-radius: 6px;
            padding: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .filiere-title {
            font-size: 10px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .filiere-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
        }
        
        .stat-item {
            text-align: center;
            padding: 5px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 3px;
            border: 1px solid rgba(44, 62, 80, 0.1);
        }
        
        .stat-value {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .stat-value.success { color: #27ae60; }
        .stat-value.warning { color: #f39c12; }
        .stat-value.primary { color: #2c3e50; }
        
        .stat-mini-label {
            font-size: 7px;
            color: #6c757d;
            text-transform: uppercase;
            font-weight: 600;
        }
        
        .chart-container {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            text-align: center;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .chart-placeholder {
            color: #6c757d;
            font-style: italic;
        }
        
        .table-container {
            margin-top: 15px;
        }
        
        .table-modern {
            width: 100%;
            border-collapse: collapse;
            font-size: 9px;
        }
        
        .table-modern th {
            background: #2c3e50;
            color: white;
            padding: 8px 6px;
            text-align: center;
            font-weight: bold;
            font-size: 8px;
            text-transform: uppercase;
        }
        
        .table-modern td {
            padding: 6px;
            border: 1px solid #dee2e6;
            text-align: center;
        }
        
        .table-modern tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .charge-bar {
            height: 12px;
            border-radius: 6px;
            overflow: hidden;
            background: #e9ecef;
            position: relative;
            margin: 2px 0;
        }
        
        .charge-fill {
            height: 100%;
            border-radius: 6px;
        }
        
        .charge-insufficient {
            background: linear-gradient(90deg, #e74c3c, #c0392b);
        }
        
        .charge-normal {
            background: linear-gradient(90deg, #27ae60, #229954);
        }
        
        .charge-excessive {
            background: linear-gradient(90deg, #f39c12, #e67e22);
        }
        
        .charge-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 7px;
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
        }
        
        .badge {
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 7px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .badge.success {
            background: #27ae60;
            color: white;
        }
        
        .badge.warning {
            background: #f39c12;
            color: white;
        }
        
        .badge.danger {
            background: #e74c3c;
            color: white;
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 2px solid #2c3e50;
            text-align: center;
            font-size: 9px;
            color: #888;
        }
        
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .kpi-card {
            background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 100%);
            border: 2px solid #2c3e50;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .kpi-value {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .kpi-label {
            font-size: 9px;
            color: #6c757d;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .kpi-trend {
            font-size: 8px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: bold;
        }
        
        .trend-up {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
        }
        
        .trend-down {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }
        
        .trend-stable {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <div class="header">
        <div class="header-left">
            <img src="{{ public_path('images/logo.png') }}" alt="Logo ENSA" class="logo-left">
            <div class="school-info">
                <div class="school-name">École Nationale des Sciences Appliquées</div>
                <div class="school-subtitle">Al Hoceima</div>
                <div class="current-date">Généré le {{ \Carbon\Carbon::now()->format('d/m/Y à H:i') }}</div>
            </div>
        </div>
        
        <div class="header-center">
            <div class="document-title">RAPPORT STATISTIQUES</div>
            <div class="year-info">{{ $selectedYear }}</div>
            <div class="subtitle">Analyse des Affectations et Charges Horaires</div>
        </div>
        
        <div class="header-right">
            <div class="export-info">
                Document généré automatiquement<br>
                Système de Gestion des Affectations<br>
                Chef de Département: {{ $chef->name }}
            </div>
        </div>
    </div>

    <!-- Statistics Overview -->
    <div class="stats-overview">
        <div class="stat-card">
            <div class="stat-icon primary">📚</div>
            <div class="stat-number">{{ $stats['total_ues'] }}</div>
            <div class="stat-label">Total UEs</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon success">✅</div>
            <div class="stat-number">{{ $stats['ues_affectees'] }}</div>
            <div class="stat-label">UEs Affectées</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon warning">⚠️</div>
            <div class="stat-number">{{ $stats['ues_vacantes'] }}</div>
            <div class="stat-label">UEs Vacantes</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon primary">👥</div>
            <div class="stat-number">{{ $stats['total_enseignants'] }}</div>
            <div class="stat-label">Enseignants</div>
        </div>
    </div>

    <!-- KPI Cards -->
    <div class="kpi-grid">
        <div class="kpi-card">
            <div class="kpi-value">{{ round(($stats['ues_affectees'] / max($stats['total_ues'], 1)) * 100) }}%</div>
            <div class="kpi-label">Taux d'Affectation</div>
            <span class="kpi-trend trend-up">📈 +5% vs année précédente</span>
        </div>

        <div class="kpi-card">
            <div class="kpi-value">{{ count($chargesHoraires) > 0 ? round(collect($chargesHoraires)->avg('charge.total')) : 0 }}h</div>
            <div class="kpi-label">Charge Moyenne</div>
            <span class="kpi-trend trend-stable">➖ Stable</span>
        </div>

        <div class="kpi-card">
            <div class="kpi-value">{{ collect($chargesHoraires)->where('status', 'insuffisant')->count() }}</div>
            <div class="kpi-label">Charges Insuffisantes</div>
            <span class="kpi-trend trend-down">📉 -2 vs mois dernier</span>
        </div>

        <div class="kpi-card">
            <div class="kpi-value">{{ $repartitionFilieres->sum('total_ues') }}</div>
            <div class="kpi-label">UEs par Filière</div>
            <span class="kpi-trend trend-up">📊 Répartition équilibrée</span>
        </div>
    </div>

    <!-- Répartition par Filière -->
    <div class="section">
        <div class="section-header">
            📊 Répartition par Filière
        </div>
        <div class="section-content">
            @if($repartitionFilieres->isNotEmpty())
                <div class="filiere-grid">
                    @foreach($repartitionFilieres as $filiere)
                        <div class="filiere-card">
                            <div class="filiere-title">{{ $filiere->filiere }}</div>
                            <div class="filiere-stats">
                                <div class="stat-item">
                                    <div class="stat-value success">{{ $filiere->ues_affectees }}</div>
                                    <div class="stat-mini-label">Affectées</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value warning">{{ $filiere->ues_vacantes }}</div>
                                    <div class="stat-mini-label">Vacantes</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value primary">{{ round(($filiere->ues_affectees / max($filiere->total_ues, 1)) * 100) }}%</div>
                                    <div class="stat-mini-label">Taux</div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="chart-container">
                    <div class="chart-placeholder">Aucune donnée de répartition disponible</div>
                </div>
            @endif
        </div>
    </div>

    <!-- Évolution des Affectations -->
    <div class="section">
        <div class="section-header">
            📈 Évolution des Affectations
        </div>
        <div class="section-content">
            @if(!empty($evolutionAffectations))
                <div class="chart-container">
                    <div style="text-align: left; font-size: 10px;">
                        <strong>Données d'évolution mensuelle:</strong><br>
                        @foreach($evolutionAffectations as $item)
                            <span style="color: #27ae60;">●</span> Mois {{ $item->mois }}: {{ $item->validees }} validées,
                            <span style="color: #f39c12;">●</span> {{ $item->en_attente }} en attente,
                            <span style="color: #e74c3c;">●</span> {{ $item->rejetees }} rejetées<br>
                        @endforeach
                    </div>
                </div>
            @else
                <div class="chart-container">
                    <div class="chart-placeholder">📊 Graphique d'évolution des affectations<br><small>Données non disponibles pour cette période</small></div>
                </div>
            @endif
        </div>
    </div>

    <!-- Charges Horaires des Enseignants -->
    <div class="section">
        <div class="section-header">
            👥 Charges Horaires des Enseignants
        </div>
        <div class="section-content">
            @if(count($chargesHoraires) > 0)
                <div class="table-container">
                    <table class="table-modern">
                        <thead>
                            <tr>
                                <th>Enseignant</th>
                                <th>Spécialité</th>
                                <th>CM</th>
                                <th>TD</th>
                                <th>TP</th>
                                <th>Total</th>
                                <th>Charge Visuelle</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($chargesHoraires as $item)
                                @php
                                    $percentage = min(100, ($item['charge']['total'] / 192) * 100);
                                    $statusClass = $item['status'] == 'insuffisant' ? 'charge-insufficient' :
                                                  ($item['status'] == 'excessif' ? 'charge-excessive' : 'charge-normal');
                                @endphp
                                <tr>
                                    <td>
                                        <strong>{{ $item['enseignant']->name }}</strong><br>
                                        <small>{{ $item['enseignant']->email }}</small>
                                    </td>
                                    <td>{{ $item['enseignant']->specialite ?? 'Non définie' }}</td>
                                    <td>{{ $item['charge']['CM'] }}h</td>
                                    <td>{{ $item['charge']['TD'] }}h</td>
                                    <td>{{ $item['charge']['TP'] }}h</td>
                                    <td><strong>{{ $item['charge']['total'] }}h</strong></td>
                                    <td>
                                        <div class="charge-bar">
                                            <div class="charge-fill {{ $statusClass }}"
                                                 style="width: {{ $percentage }}%"></div>
                                            <div class="charge-text">{{ $item['charge']['total'] }}/192h</div>
                                        </div>
                                    </td>
                                    <td>
                                        @if($item['status'] == 'insuffisant')
                                            <span class="badge danger">Insuffisant</span>
                                        @elseif($item['status'] == 'excessif')
                                            <span class="badge warning">Excessif</span>
                                        @else
                                            <span class="badge success">Normal</span>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="chart-container">
                    <div class="chart-placeholder">👥 Aucune donnée de charge horaire disponible</div>
                </div>
            @endif
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p><strong>Ce document a été généré automatiquement par le Système de Gestion des Affectations d'Enseignement</strong></p>
        <p>ENSA Al Hoceima - École Nationale des Sciences Appliquées - Rapport {{ $selectedYear }}</p>
        <p>Chef de Département: {{ $chef->name }} | Généré le {{ \Carbon\Carbon::now()->format('d/m/Y à H:i') }}</p>
    </div>
</body>
</html>
