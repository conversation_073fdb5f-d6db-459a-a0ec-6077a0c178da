<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Http\Controllers\Admin\vacataire\VacataireController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

echo "=== DEBUGGING VACATAIRE PDF EXPORT ISSUE ===\n\n";

// Test with Kari
$kari = User::where('email', '<EMAIL>')->first();

if (!$kari) {
    echo "❌ Kari not found!\n";
    exit;
}

echo "✅ Testing with Kari: {$kari->name}\n";

// Simulate authentication
Auth::login($kari);

echo "=== 1. TESTING ROUTE EXISTENCE ===\n";

try {
    $exportUrl = route('vacataire.emploi-du-temps.export');
    echo "✅ Export route exists: {$exportUrl}\n";
} catch (\Exception $e) {
    echo "❌ Export route error: " . $e->getMessage() . "\n";
    echo "This could be why the button doesn't work!\n";
}

echo "\n=== 2. TESTING CONTROLLER METHOD DIRECTLY ===\n";

try {
    $controller = new VacataireController();
    $request = new Request();
    
    echo "Calling exportEmploiDuTemps method...\n";
    $response = $controller->exportEmploiDuTemps($request);
    
    echo "✅ Controller method executed successfully\n";
    echo "✅ Response type: " . get_class($response) . "\n";
    
    // Check if it's a download response
    if (method_exists($response, 'getContent')) {
        $content = $response->getContent();
        $contentLength = strlen($content);
        echo "✅ Content length: {$contentLength} bytes\n";
        
        if (strpos($content, '%PDF') === 0) {
            echo "✅ Valid PDF content generated\n";
        } else {
            echo "❌ Content is not PDF format\n";
            echo "First 100 chars: " . substr($content, 0, 100) . "\n";
        }
    }
    
    if (method_exists($response, 'headers')) {
        $headers = $response->headers->all();
        echo "✅ Response headers:\n";
        foreach ($headers as $key => $value) {
            if (is_array($value)) {
                echo "  - {$key}: " . implode(', ', $value) . "\n";
            } else {
                echo "  - {$key}: {$value}\n";
            }
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Controller method error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 3. TESTING DOMPDF LIBRARY ===\n";

try {
    if (class_exists('Barryvdh\DomPDF\Facade\Pdf')) {
        echo "✅ DomPDF library is available\n";
        
        // Test basic PDF generation
        $testPdf = \Barryvdh\DomPDF\Facade\Pdf::loadHTML('<h1>Test PDF</h1>');
        $testContent = $testPdf->output();
        
        if (strpos($testContent, '%PDF') === 0) {
            echo "✅ Basic PDF generation works\n";
        } else {
            echo "❌ Basic PDF generation failed\n";
        }
        
    } else {
        echo "❌ DomPDF library not available\n";
        echo "This is likely the main issue!\n";
    }
} catch (\Exception $e) {
    echo "❌ DomPDF test error: " . $e->getMessage() . "\n";
}

echo "\n=== 4. TESTING PDF TEMPLATE ===\n";

try {
    $templatePath = 'vacataire.exports.emploi-du-temps';
    
    // Check if template exists
    if (view()->exists($templatePath)) {
        echo "✅ PDF template exists: {$templatePath}\n";
        
        // Try to render template
        $view = view($templatePath, [
            'vacataire' => $kari,
            'schedules' => collect([]),
            'title' => 'Test',
            'currentDate' => date('d/m/Y'),
            'academicYear' => date('Y') . '-' . (date('Y') + 1)
        ]);
        
        $html = $view->render();
        echo "✅ Template renders successfully (" . strlen($html) . " chars)\n";
        
    } else {
        echo "❌ PDF template missing: {$templatePath}\n";
        echo "This could be why PDF generation fails!\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Template test error: " . $e->getMessage() . "\n";
}

echo "\n=== 5. TESTING CSRF TOKEN ===\n";

try {
    $csrfToken = csrf_token();
    echo "✅ CSRF token available: " . substr($csrfToken, 0, 10) . "...\n";
} catch (\Exception $e) {
    echo "❌ CSRF token error: " . $e->getMessage() . "\n";
}

echo "\n=== 6. TESTING LOGO FILE ===\n";

$logoPath = public_path('images/logo.png');
if (file_exists($logoPath)) {
    echo "✅ Logo file exists: {$logoPath}\n";
    echo "✅ Logo file size: " . filesize($logoPath) . " bytes\n";
} else {
    echo "❌ Logo file missing: {$logoPath}\n";
    echo "This could cause PDF generation to fail!\n";
}

echo "\n=== 7. TESTING SCHEDULE DATA ===\n";

// Check what data Kari has
$affectations = App\Models\Affectation::where('user_id', $kari->id)
    ->where('validee', 'valide')
    ->with('uniteEnseignement')
    ->get();

echo "✅ Kari's affectations: " . $affectations->count() . "\n";

if ($affectations->count() > 0) {
    foreach ($affectations as $affectation) {
        echo "- {$affectation->uniteEnseignement->code} ({$affectation->type_seance})\n";
    }
    
    $schedules = App\Models\Schedule::where('user_id', $kari->id)
        ->with('uniteEnseignement')
        ->get();
    
    echo "\n✅ Kari's schedules: " . $schedules->count() . "\n";
    
    if ($schedules->count() > 0) {
        foreach ($schedules as $schedule) {
            echo "- {$schedule->uniteEnseignement->code} ({$schedule->type_seance}) - {$schedule->jour_semaine} {$schedule->heure_debut}-{$schedule->heure_fin}\n";
        }
    } else {
        echo "⚠️  No schedules found - PDF will be empty\n";
    }
} else {
    echo "❌ No affectations found - this could be the issue!\n";
}

echo "\n=== 8. TESTING BROWSER COMPATIBILITY ===\n";

echo "JavaScript function analysis:\n";
echo "✅ Function name: exportEmploiDuTempsPdf()\n";
echo "✅ Uses window.open() for popup\n";
echo "✅ Creates form with POST method\n";
echo "✅ Includes CSRF token\n";
echo "✅ Targets new window\n";
echo "✅ Has 3-second timeout\n";

echo "\nPossible issues:\n";
echo "- Pop-up blocker preventing window.open()\n";
echo "- CSRF token mismatch\n";
echo "- Route not found\n";
echo "- Controller method throwing exception\n";
echo "- DomPDF library missing\n";
echo "- PDF template missing\n";
echo "- No data to export\n";

echo "\n=== FINAL DIAGNOSIS ===\n";

$issues = [];

// Check critical components
if (!route('vacataire.emploi-du-temps.export')) {
    $issues[] = "Export route missing";
}

if (!class_exists('Barryvdh\DomPDF\Facade\Pdf')) {
    $issues[] = "DomPDF library missing";
}

if (!view()->exists('vacataire.exports.emploi-du-temps')) {
    $issues[] = "PDF template missing";
}

if (!file_exists($logoPath)) {
    $issues[] = "Logo file missing";
}

if ($affectations->count() === 0) {
    $issues[] = "No affectations for user";
}

if (empty($issues)) {
    echo "🎉 ALL COMPONENTS SEEM OK - ISSUE MIGHT BE FRONTEND\n";
    echo "🔍 Check browser console for JavaScript errors\n";
    echo "🔍 Check if pop-ups are blocked\n";
    echo "🔍 Try opening developer tools and clicking button\n";
} else {
    echo "❌ ISSUES FOUND:\n";
    foreach ($issues as $issue) {
        echo "   - {$issue}\n";
    }
}

echo "\n=== DEBUG COMPLETE ===\n";
