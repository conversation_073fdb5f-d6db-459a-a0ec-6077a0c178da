<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\UniteEnseignement;
use App\Models\Affectation;

echo "=== ANALYZING ENSEIGNANT SPECIALITIES vs UE SPECIALITIES ===\n\n";

// Get all enseignants with their specialities
$enseignants = User::where('role', 'enseignant')
    ->whereNotNull('specialite')
    ->where('specialite', '!=', '')
    ->with(['affectations' => function($query) {
        $query->where('annee_universitaire', '2025-2026')
              ->where('validee', 'valide')
              ->with('uniteEnseignement');
    }])
    ->get();

echo "Found " . $enseignants->count() . " enseignants with specialities\n\n";

// Get all UEs with their specialities
$ues = UniteEnseignement::whereNotNull('specialite')
    ->where('specialite', '!=', '')
    ->with(['filiere', 'departement', 'affectations' => function($query) {
        $query->where('annee_universitaire', '2025-2026')
              ->where('validee', 'valide')
              ->with('user');
    }])
    ->get();

echo "Found " . $ues->count() . " UEs with specialities\n\n";

// Parse enseignant specialities
$enseignantSpecialities = [];
$enseignantDetails = [];

foreach ($enseignants as $enseignant) {
    $specialities = array_map('trim', explode(',', $enseignant->specialite));
    $specialities = array_filter($specialities);
    
    $enseignantDetails[$enseignant->id] = [
        'name' => $enseignant->name,
        'email' => $enseignant->email,
        'specialities' => $specialities,
        'affectations' => $enseignant->affectations
    ];
    
    foreach ($specialities as $spec) {
        if (!in_array($spec, $enseignantSpecialities)) {
            $enseignantSpecialities[] = $spec;
        }
    }
}

sort($enseignantSpecialities);

// Parse UE specialities
$ueSpecialities = [];
$ueDetails = [];

foreach ($ues as $ue) {
    $specialities = array_map('trim', explode(',', $ue->specialite));
    $specialities = array_filter($specialities);
    
    $ueDetails[$ue->id] = [
        'code' => $ue->code,
        'nom' => $ue->nom,
        'filiere' => $ue->filiere->nom ?? 'Unknown',
        'specialities' => $specialities,
        'affectations' => $ue->affectations,
        'est_vacant' => $ue->est_vacant,
        'vacataire_types' => $ue->vacataire_types
    ];
    
    foreach ($specialities as $spec) {
        if (!in_array($spec, $ueSpecialities)) {
            $ueSpecialities[] = $spec;
        }
    }
}

sort($ueSpecialities);

echo "=== ENSEIGNANT SPECIALITIES ===\n";
echo "Total unique enseignant specialities: " . count($enseignantSpecialities) . "\n\n";
foreach ($enseignantSpecialities as $i => $spec) {
    echo ($i + 1) . ". {$spec}\n";
}

echo "\n=== UE SPECIALITIES ===\n";
echo "Total unique UE specialities: " . count($ueSpecialities) . "\n\n";
foreach ($ueSpecialities as $i => $spec) {
    echo ($i + 1) . ". {$spec}\n";
}

// Compare specialities
$matchingSpecialities = [];
$enseignantOnlySpecialities = [];
$ueOnlySpecialities = [];

// Check enseignant specialities against UE specialities
foreach ($enseignantSpecialities as $ensSpec) {
    $found = false;
    foreach ($ueSpecialities as $ueSpec) {
        if (strcasecmp($ensSpec, $ueSpec) === 0) {
            $matchingSpecialities[] = $ensSpec;
            $found = true;
            break;
        }
    }
    if (!$found) {
        $enseignantOnlySpecialities[] = $ensSpec;
    }
}

// Check UE specialities not in enseignants
foreach ($ueSpecialities as $ueSpec) {
    $found = false;
    foreach ($enseignantSpecialities as $ensSpec) {
        if (strcasecmp($ueSpec, $ensSpec) === 0) {
            $found = true;
            break;
        }
    }
    if (!$found) {
        $ueOnlySpecialities[] = $ueSpec;
    }
}

echo "\n=== COMPARISON RESULTS ===\n\n";

echo "✅ MATCHING SPECIALITIES (" . count($matchingSpecialities) . "):\n";
foreach ($matchingSpecialities as $i => $spec) {
    echo ($i + 1) . ". {$spec}\n";
}

echo "\n❌ ENSEIGNANT-ONLY SPECIALITIES (NOT in any UE) (" . count($enseignantOnlySpecialities) . "):\n";
foreach ($enseignantOnlySpecialities as $i => $spec) {
    echo ($i + 1) . ". {$spec}\n";
}

echo "\n❌ UE-ONLY SPECIALITIES (NO enseignant has these) (" . count($ueOnlySpecialities) . "):\n";
foreach ($ueOnlySpecialities as $i => $spec) {
    echo ($i + 1) . ". {$spec}\n";
}

$matchPercentage = count($ueSpecialities) > 0 ? 
    round((count($matchingSpecialities) / count($ueSpecialities)) * 100, 2) : 0;

echo "\n=== STATISTICS ===\n";
echo "Total enseignant specialities: " . count($enseignantSpecialities) . "\n";
echo "Total UE specialities: " . count($ueSpecialities) . "\n";
echo "Matching specialities: " . count($matchingSpecialities) . "\n";
echo "Enseignant-only specialities: " . count($enseignantOnlySpecialities) . "\n";
echo "UE-only specialities: " . count($ueOnlySpecialities) . "\n";
echo "Match percentage: {$matchPercentage}%\n";

echo "\n=== CURRENT AFFECTATIONS ANALYSIS ===\n\n";

// Analyze current affectations
$successfulMatches = 0;
$problematicAffectations = [];

foreach ($enseignants as $enseignant) {
    if ($enseignant->affectations->count() > 0) {
        echo "--- {$enseignant->name} ---\n";
        echo "Enseignant specialities: " . implode(', ', $enseignantDetails[$enseignant->id]['specialities']) . "\n";
        echo "Current affectations:\n";
        
        foreach ($enseignant->affectations as $affectation) {
            $ue = $affectation->uniteEnseignement;
            if ($ue && $ue->specialite) {
                $ueSpecs = array_map('trim', explode(',', $ue->specialite));
                $ueSpecs = array_filter($ueSpecs);
                
                // Check if enseignant specialities match UE specialities
                $hasMatch = false;
                $ensSpecs = $enseignantDetails[$enseignant->id]['specialities'];
                
                foreach ($ensSpecs as $ensSpec) {
                    foreach ($ueSpecs as $ueSpec) {
                        if (stripos($ensSpec, $ueSpec) !== false || stripos($ueSpec, $ensSpec) !== false) {
                            $hasMatch = true;
                            break 2;
                        }
                    }
                }
                
                $status = $hasMatch ? "✅ MATCH" : "❌ MISMATCH";
                echo "  - {$ue->code} ({$ue->nom}) - {$affectation->type_seance}\n";
                echo "    UE specialities: " . implode(', ', $ueSpecs) . "\n";
                echo "    Status: {$status}\n";
                
                if ($hasMatch) {
                    $successfulMatches++;
                } else {
                    $problematicAffectations[] = [
                        'enseignant' => $enseignant->name,
                        'enseignant_specs' => $ensSpecs,
                        'ue_code' => $ue->code,
                        'ue_nom' => $ue->nom,
                        'ue_specs' => $ueSpecs,
                        'type_seance' => $affectation->type_seance
                    ];
                }
            }
        }
        echo "\n";
    }
}

echo "=== AFFECTATION QUALITY ANALYSIS ===\n\n";
echo "Total affectations analyzed: " . ($successfulMatches + count($problematicAffectations)) . "\n";
echo "Successful matches: {$successfulMatches}\n";
echo "Problematic affectations: " . count($problematicAffectations) . "\n";

if (count($problematicAffectations) > 0) {
    $affectationMatchPercentage = round(($successfulMatches / ($successfulMatches + count($problematicAffectations))) * 100, 2);
    echo "Affectation match percentage: {$affectationMatchPercentage}%\n\n";
    
    echo "🚨 PROBLEMATIC AFFECTATIONS:\n";
    foreach ($problematicAffectations as $i => $prob) {
        echo ($i + 1) . ". {$prob['enseignant']} → {$prob['ue_code']} ({$prob['type_seance']})\n";
        echo "   Enseignant: " . implode(', ', $prob['enseignant_specs']) . "\n";
        echo "   UE: " . implode(', ', $prob['ue_specs']) . "\n\n";
    }
}

echo "=== UNASSIGNED UEs ANALYSIS ===\n\n";

// Find UEs with no affectations
$unassignedUEs = [];
foreach ($ues as $ue) {
    if ($ue->affectations->count() === 0) {
        $unassignedUEs[] = $ue;
    }
}

echo "Unassigned UEs: " . count($unassignedUEs) . "\n\n";

if (count($unassignedUEs) > 0) {
    echo "🔍 UNASSIGNED UEs BY SPECIALITY:\n";
    $unassignedBySpec = [];
    
    foreach ($unassignedUEs as $ue) {
        $specs = array_map('trim', explode(',', $ue->specialite));
        $specs = array_filter($specs);
        
        foreach ($specs as $spec) {
            if (!isset($unassignedBySpec[$spec])) {
                $unassignedBySpec[$spec] = [];
            }
            $unassignedBySpec[$spec][] = $ue->code . " (" . $ue->filiere->nom . ")";
        }
    }
    
    foreach ($unassignedBySpec as $spec => $ueList) {
        echo "\n{$spec} (" . count($ueList) . " UEs):\n";
        foreach ($ueList as $ueInfo) {
            echo "  - {$ueInfo}\n";
        }
    }
}

echo "\n=== RECOMMENDATIONS ===\n\n";

if (count($ueOnlySpecialities) > 0) {
    echo "🎯 RECRUIT ENSEIGNANTS WITH THESE SPECIALITIES:\n";
    foreach ($ueOnlySpecialities as $spec) {
        $ueCount = 0;
        foreach ($ues as $ue) {
            $ueSpecs = array_map('trim', explode(',', $ue->specialite));
            if (in_array($spec, $ueSpecs)) {
                $ueCount++;
            }
        }
        echo "- {$spec} (needed for {$ueCount} UEs)\n";
    }
    echo "\n";
}

if (count($enseignantOnlySpecialities) > 0) {
    echo "💡 CREATE UEs FOR THESE ENSEIGNANT SPECIALITIES:\n";
    foreach ($enseignantOnlySpecialities as $spec) {
        echo "- {$spec}\n";
    }
    echo "\n";
}

echo "=== ANALYSIS COMPLETE ===\n";
