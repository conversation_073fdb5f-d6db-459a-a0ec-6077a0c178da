<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Models\Affectation;
use App\Http\Controllers\Admin\vacataire\VacataireController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

echo "=== TESTING VACATAIRE UEs FUNCTIONALITY ===\n\n";

// Find Kari specifically
$vacataire = User::where('email', '<EMAIL>')->first();

if (!$vacataire) {
    echo "❌ No vacataire with affectations found!\n";
    exit;
}

echo "✅ Testing with vacataire: {$vacataire->name} (ID: {$vacataire->id})\n\n";

// Simulate authentication
Auth::login($vacataire);

// Test the controller method
echo "=== TESTING CONTROLLER METHOD ===\n";

try {
    $controller = new VacataireController();
    $request = new Request();
    
    // Call the unitesEnseignement method
    $response = $controller->unitesEnseignement($request);
    
    // Extract data from the response
    $viewData = $response->getData();
    $uesGrouped = $viewData['uesGrouped'];
    $filieres = $viewData['filieres'];
    
    echo "✅ Controller method executed successfully\n";
    echo "✅ UEs grouped count: " . $uesGrouped->count() . "\n";
    echo "✅ Available filieres: " . $filieres->count() . "\n\n";
    
    if ($uesGrouped->count() > 0) {
        echo "=== SAMPLE UE GROUP DATA ===\n";
        $firstUE = $uesGrouped->first();
        
        echo "UE ID: {$firstUE->id}\n";
        echo "UE Code: {$firstUE->ue->code}\n";
        echo "UE Name: {$firstUE->ue->nom}\n";
        echo "Session Types: " . implode(', ', $firstUE->session_types->toArray()) . "\n";
        echo "Total Affectations: {$firstUE->total_affectations}\n";
        echo "Filiere: " . ($firstUE->ue->filiere->nom ?? 'N/A') . "\n\n";
        
        // Test UE details method
        echo "=== TESTING UE DETAILS METHOD ===\n";
        
        try {
            $detailsResponse = $controller->ueDetails($firstUE->ue->id);
            $detailsData = $detailsResponse->getData();
            
            echo "✅ UE Details method executed successfully\n";
            echo "✅ UE: {$detailsData['ue']->code} - {$detailsData['ue']->nom}\n";
            echo "✅ Affectations count: " . $detailsData['affectations']->count() . "\n";
            echo "✅ Session types: " . implode(', ', $detailsData['sessionTypes']->toArray()) . "\n";
            echo "✅ Schedules count: " . $detailsData['schedules']->count() . "\n";
            echo "✅ Notes count: " . $detailsData['notes']->count() . "\n\n";
            
        } catch (\Exception $e) {
            echo "❌ UE Details method failed: " . $e->getMessage() . "\n\n";
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Controller method failed: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n\n";
}

// Test data structure for frontend
echo "=== TESTING DATA STRUCTURE FOR FRONTEND ===\n";

$currentYear = date('Y') . '-' . (date('Y') + 1);

// Get affectations directly
$affectations = Affectation::where('user_id', $vacataire->id)
    ->where('annee_universitaire', $currentYear)
    ->where('validee', 'valide')
    ->with(['uniteEnseignement.filiere'])
    ->get();

echo "Total affectations: " . $affectations->count() . "\n";

// Group by UE
$groupedUEs = $affectations->groupBy('ue_id')->map(function ($affectations) {
    $firstAffectation = $affectations->first();
    $ue = $firstAffectation->uniteEnseignement;
    
    $sessionTypes = $affectations->pluck('type_seance')->unique()->sort()->values();
    
    return (object) [
        'id' => $ue->id,
        'ue' => $ue,
        'session_types' => $sessionTypes,
        'affectations' => $affectations,
        'total_affectations' => $affectations->count()
    ];
});

echo "Grouped UEs count: " . $groupedUEs->count() . "\n\n";

if ($groupedUEs->count() > 0) {
    echo "=== GROUPED UEs ANALYSIS ===\n";
    
    foreach ($groupedUEs as $ueGroup) {
        echo "--- {$ueGroup->ue->code} ---\n";
        echo "Name: {$ueGroup->ue->nom}\n";
        echo "Filiere: " . ($ueGroup->ue->filiere->nom ?? 'N/A') . "\n";
        echo "Session Types: " . implode(', ', $ueGroup->session_types->toArray()) . "\n";
        echo "Total Affectations: {$ueGroup->total_affectations}\n";
        echo "Hours: CM={$ueGroup->ue->heures_cm}, TD={$ueGroup->ue->heures_td}, TP={$ueGroup->ue->heures_tp}\n";
        echo "Specialite: " . ($ueGroup->ue->specialite ?? 'None') . "\n\n";
    }
}

// Test route accessibility
echo "=== TESTING ROUTE ACCESSIBILITY ===\n";

try {
    $routes = app('router')->getRoutes();
    $vacataireRoutes = 0;
    $ueRoutes = 0;
    
    foreach ($routes as $route) {
        if (strpos($route->uri(), 'vacataire') !== false) {
            $vacataireRoutes++;
            if (strpos($route->uri(), 'ue') !== false || strpos($route->uri(), 'unites-enseignement') !== false) {
                $ueRoutes++;
            }
        }
    }
    
    echo "✅ Total vacataire routes: {$vacataireRoutes}\n";
    echo "✅ UE-related routes: {$ueRoutes}\n";
    
    // Check specific routes
    $requiredRoutes = [
        'vacataire.unites-enseignement',
        'vacataire.ue.details'
    ];
    
    foreach ($requiredRoutes as $routeName) {
        try {
            $url = route($routeName, ['id' => 1]);
            echo "✅ Route '{$routeName}' exists: {$url}\n";
        } catch (\Exception $e) {
            echo "❌ Route '{$routeName}' missing or invalid\n";
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Route testing failed: " . $e->getMessage() . "\n";
}

echo "\n=== FRONTEND COMPATIBILITY CHECK ===\n";

// Check if the data structure matches what the frontend expects
$frontendCompatible = true;
$issues = [];

if ($groupedUEs->count() > 0) {
    $sampleUE = $groupedUEs->first();
    
    // Check required properties
    $requiredProps = ['id', 'ue', 'session_types', 'affectations', 'total_affectations'];
    
    foreach ($requiredProps as $prop) {
        if (!property_exists($sampleUE, $prop)) {
            $issues[] = "Missing property: {$prop}";
            $frontendCompatible = false;
        }
    }
    
    // Check UE object properties
    $requiredUEProps = ['id', 'code', 'nom', 'filiere', 'heures_cm', 'heures_td', 'heures_tp'];
    
    foreach ($requiredUEProps as $prop) {
        if (!property_exists($sampleUE->ue, $prop)) {
            $issues[] = "Missing UE property: {$prop}";
            $frontendCompatible = false;
        }
    }
    
    // Check session_types is a collection
    if (!is_object($sampleUE->session_types) || !method_exists($sampleUE->session_types, 'toArray')) {
        $issues[] = "session_types is not a proper collection";
        $frontendCompatible = false;
    }
}

if ($frontendCompatible) {
    echo "✅ Frontend data structure: Compatible\n";
    echo "✅ All required properties present\n";
    echo "✅ Data types correct\n";
} else {
    echo "❌ Frontend compatibility issues found:\n";
    foreach ($issues as $issue) {
        echo "   - {$issue}\n";
    }
}

echo "\n=== FINAL SYSTEM STATUS ===\n";

$overallStatus = true;
$systemIssues = [];

// Check critical components
if (!$vacataire) {
    $systemIssues[] = "No vacataire found";
    $overallStatus = false;
}

if ($groupedUEs->count() === 0) {
    $systemIssues[] = "No UEs found for vacataire";
    $overallStatus = false;
}

if (!$frontendCompatible) {
    $systemIssues[] = "Frontend compatibility issues";
    $overallStatus = false;
}

if ($overallStatus) {
    echo "🎉 SYSTEM STATUS: ✅ FULLY OPERATIONAL\n";
    echo "🎉 Vacataire UEs management working perfectly!\n";
    echo "🎉 Grouped UEs display functioning correctly\n";
    echo "🎉 UE details view ready\n";
    echo "🎉 Frontend compatibility ensured\n";
    echo "🎉 All session types properly displayed\n";
} else {
    echo "⚠️  SYSTEM STATUS: ❌ ISSUES DETECTED\n";
    echo "Issues found:\n";
    foreach ($systemIssues as $issue) {
        echo "   - {$issue}\n";
    }
}

echo "\n=== TEST COMPLETE ===\n";
