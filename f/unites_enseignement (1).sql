-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Hôte : 127.0.0.1
-- <PERSON><PERSON><PERSON><PERSON> le : ven. 13 juin 2025 à 01:53
-- Version du serveur : 10.4.32-MariaDB
-- Version de PHP : 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données : `gst`
--

-- --------------------------------------------------------

--
-- Structure de la table `unites_enseignement`
--

CREATE TABLE `unites_enseignement` (
  `id` int(11) NOT NULL,
  `nom` varchar(255) NOT NULL,
  `specialite` varchar(255) DEFAULT NULL,
  `code` varchar(100) NOT NULL,
  `heures_cm` int(11) NOT NULL DEFAULT 0,
  `heures_td` int(11) NOT NULL DEFAULT 0,
  `heures_tp` int(11) NOT NULL DEFAULT 0,
  `semestre` enum('S1','S2','S3','S4','S5','S6') NOT NULL,
  `est_vacant` tinyint(1) NOT NULL,
  `vacataire_types` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`vacataire_types`)),
  `groupes_td` int(11) NOT NULL DEFAULT 0,
  `groupes_tp` int(11) NOT NULL DEFAULT 0,
  `filiere_id` int(11) NOT NULL,
  `departement_id` int(11) NOT NULL,
  `responsable_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `unites_enseignement`
--

INSERT INTO `unites_enseignement` (`id`, `nom`, `specialite`, `code`, `heures_cm`, `heures_td`, `heures_tp`, `semestre`, `est_vacant`, `vacataire_types`, `groupes_td`, `groupes_tp`, `filiere_id`, `departement_id`, `responsable_id`, `created_at`, `updated_at`) VALUES
(3, 'Architecture des ordinateurs', 'Général', 'M111', 26, 16, 16, 'S1', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(4, 'Langage C avancé et structures de données', 'Génie Civil,Structures', 'M112', 26, 16, 18, 'S1', 0, '[\"TP\"]', 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-12 01:17:56'),
(5, 'Recherche opérationnelle et théorie des graphes', 'Général', 'M113', 26, 24, 12, 'S1', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(6, 'Systèmes d\'Information et Bases de Données Relationnelles', 'Informatique,Systèmes', 'M114', 26, 24, 12, 'S1', 0, '[\"TD\",\"TP\"]', 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-12 02:17:41'),
(7, 'Réseaux informatiques', 'Informatique,Programmation,Réseaux', 'M115', 26, 18, 14, 'S1', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(8, 'Culture and Art skills', 'Général', 'M116', 26, 10, 0, 'S1', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(9, 'Langues Etrangéres (Français)', 'Langues,Français', 'M117.1', 20, 6, 3, 'S1', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(10, 'Langues Etrangéres (Anglais)', 'Langues,Anglais', 'M117.2', 20, 6, 3, 'S1', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(11, 'Architecture Logicielle et UML', 'Général', 'M121', 26, 16, 10, 'S2', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(12, 'Web1 : Technologies de Web et PHP5', 'Informatique,Développement Web', 'M122', 26, 10, 16, 'S2', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(13, 'Programmation Orientée Objet C++', 'Informatique,Programmation', 'M123', 26, 16, 10, 'S2', 0, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(14, 'Linux et programmation systéme', 'Informatique,Programmation', 'M124', 26, 16, 10, 'S2', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(15, 'Algorithmique Avancée et complexité', 'Général', 'M125', 26, 26, 4, 'S2', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(16, 'Prompt ingeniering for developpers', 'Général', 'M126', 26, 26, 6, 'S2', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(17, 'Langues,Communication et TIC -fr', 'Communication,Langues', 'M127.1', 20, 6, 3, 'S2', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(18, 'Langues,Communication et TIC- Ang', 'Communication,Langues', 'M127.2', 20, 6, 3, 'S2', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(19, 'Python pour les sciences de données', 'Informatique,Programmation', 'M31', 28, 0, 36, 'S3', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(20, 'Programmation Java Avancée', 'Informatique,Programmation', 'M32', 24, 8, 32, 'S3', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(21, 'Langues et Communication -FR', 'Communication,Langues', 'M33.1', 21, 0, 11, 'S3', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(22, 'Langues et Communication- Ang', 'Communication,Langues', 'M33.2', 21, 10, 0, 'S3', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(23, 'Langues et Communication- Espagnol', 'Communication,Langues', 'M33.3', 21, 10, 0, 'S3', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(24, 'Linux et programmation système', 'Informatique,Programmation,Systèmes', 'M34', 21, 16, 27, 'S3', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(25, 'Administration des Bases de données Avancées', 'Général', 'M35', 26, 4, 34, 'S3', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(26, 'Administration réseaux et systèmes', 'Informatique,Réseaux,Systèmes', 'M36', 27, 15, 22, 'S3', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(27, 'Entreprenariat 2 - Contrôle gestion', 'Informatique,IA', 'M41', 21, 18, 0, 'S4', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(28, 'Machine Learning', 'Informatique,IA', 'M42', 21, 20, 23, 'S4', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(29, 'Gestion de projet', 'Projet', 'M43.1', 16, 6, 16, 'S4', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(30, 'Génie logiciel', 'Général', 'M43.2', 12, 6, 0, 'S4', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(31, 'Crypto-systèmes', 'Informatique,Systèmes', 'M44.1', 15, 10, 4, 'S4', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(32, 'sécurité Informatique', 'Informatique,Programmation,Réseaux,Sécurité', 'M44.2', 15, 10, 10, 'S4', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(33, 'Frameworks Java EE avancés', 'Informatique,Programmation', 'M45.1', 15, 10, 4, 'S4', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(34, '.Net', 'Général', 'M45.2', 15, 10, 10, 'S4', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(35, 'Web 2 : Applications Web modernes', 'Informatique,Développement Web', 'M46', 21, 15, 28, 'S4', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(36, 'Système embarqué et temps réel', 'Informatique,Systèmes', 'M51', 25, 25, 14, 'S5', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(37, 'Développement des applications mobiles', 'Général', 'M52', 28, 0, 36, 'S5', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(38, 'Virtualisation', 'Général', 'M53.1', 10, 4, 12, 'S5', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(39, 'Cloud Computing', 'Général', 'M53.2', 12, 8, 18, 'S5', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(40, 'Analyse et conception des systèmes décisionnels', 'Informatique,Systèmes,Mathématiques,Analyse', 'M54', 28, 12, 24, 'S5', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(41, 'Enterprise Resource Planning ERP', 'Général', 'M55', 22, 12, 30, 'S5', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(42, 'Ingénierie logicielle, Qualité, Test et Intégration', 'Général', 'M56', 21, 18, 25, 'S5', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(43, 'Ingénierie de l\'information et des connaissances', 'Général', 'M57', 28, 12, 24, 'S5', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(44, 'Business Intelligence & Veille Stratégique', 'Général', 'M58', 24, 16, 24, 'S5', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(45, 'Data Mining', 'Informatique,Data Science', 'M59', 26, 14, 24, 'S5', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(46, 'Entreprenariat 3 -RH', 'Informatique,IA', 'M510', 30, 0, 0, 'S5', 1, NULL, 2, 2, 1, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(47, 'Théorie des langages et compilation', 'Général', 'T111', 26, 18, 8, 'S1', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(48, 'Systèmes d\'Information et Bases de Données', 'Informatique,Systèmes', 'T112', 26, 10, 16, 'S1', 0, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(49, 'Structure de données et Algorithmique avancée', 'Génie Civil,Structures', 'T113', 26, 16, 10, 'S1', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(50, 'Architecture d\'entreprise et transformation digitale', 'Général', 'T114', 26, 6, 20, 'S1', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(51, 'Architecture des ordinateurs et systèmes d\'exploitation', 'Informatique,Systèmes', 'T115', 26, 10, 16, 'S1', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(52, 'Langues Etrangéres (Anglais/Français)', 'Langues,Anglais,Français', 'T116', 44, 12, 6, 'S1', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(53, 'Compétences en culture et en art', 'Général', 'T117', 26, 10, 0, 'S1', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(54, 'Programmation Orientée Objet Java', 'Informatique,Programmation', 'T122', 24, 10, 30, 'S2', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(55, 'Programmation Python / Programmation fonctionnelle', 'Informatique,Programmation', 'T127', 36, 0, 28, 'S2', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(56, 'Développement Web', 'Informatique,Développement Web', 'T121', 24, 0, 40, 'S2', 1, '[\"CM\",\"TP\"]', 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-12 21:26:46'),
(57, 'Gestion de projets informatiques', 'Informatique,Programmation,Réseaux,Projet', 'T125', 24, 10, 30, 'S2', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(58, 'Industrie de numérique', 'Général', 'T123', 24, 20, 20, 'S2', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(59, 'Langues Etrangéres (Anglais /Français)', 'Langues,Anglais,Français', 'T124', 44, 12, 6, 'S2', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(60, 'marketing et management pour les technologies de l\'information', 'Management', 'T126', 24, 30, 10, 'S2', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(61, 'Cloud Computing', 'Général', 'T234', 24, 10, 30, 'S3', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(62, 'Cartographie des systèmes d\'information', 'Informatique,Systèmes', 'T232', 24, 20, 20, 'S3', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(63, 'Bases de l\'Intelligence Artificielle', 'Informatique,IA', 'T235', 24, 10, 30, 'S3', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(64, 'Architecture logiciel et UML', 'Général', 'T236', 24, 10, 30, 'S3', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(65, 'Communication Professionnelle et Soft Skills-2', 'Communication', 'T233', 30, 30, 0, 'S3', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(66, 'Gestion de projets digitaux', 'Projet', 'T231', 24, 30, 10, 'S3', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(67, 'Applications de l\'Intelligence Artificielle', 'Informatique,IA', 'T243', 24, 10, 30, 'S4', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(68, 'Ingestion et stockage de données', 'Général', 'T245', 24, 10, 30, 'S4', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(69, 'Big Data', 'Informatique,Data Science', 'T242', 24, 10, 30, 'S4', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(70, 'Droit et sécurité des données', 'Informatique,Sécurité,Droit', 'T244', 24, 20, 20, 'S4', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(71, 'Cyber Security', 'Général', 'T241', 24, 10, 30, 'S4', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(72, 'Entreprenariat', 'Informatique,IA', 'T246', 24, 20, 10, 'S4', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(73, 'La veille Stratégique, Scientifique et Technologique', 'Général', 'T351', 24, 10, 30, 'S5', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(74, 'Gouvernance et Urbanisation des SI', 'Général', 'T353', 24, 10, 30, 'S5', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(75, 'DevOps', 'Général', 'T352', 24, 10, 30, 'S5', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(76, 'Innovation Engineering & Digitalisation', 'Général', 'T355', 24, 10, 30, 'S5', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(77, 'Web Marketing et CRM', 'Informatique,Développement Web', 'T354', 24, 10, 30, 'S5', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(78, 'Business English', 'Général', 'T356', 14, 20, 0, 'S5', 1, NULL, 2, 2, 2, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(79, 'Analyse numérique matricielle', 'Mathématiques,Analyse', 'ID111.1', 0, 0, 0, 'S1', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(80, 'statistique inférentielle', 'Général', 'ID111.2', 0, 0, 0, 'S1', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(81, 'Théorie des langages et compilation', 'Général', 'ID112', 0, 0, 0, 'S1', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(82, 'Systèmes d\'Information et Bases de données', 'Informatique,Systèmes', 'ID113', 0, 0, 0, 'S1', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(83, 'Relationnelles', 'Général', 'ID114', 0, 0, 0, 'S1', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(84, 'Architectures des ordinateurs et systèmes d\'exploitation', 'Informatique,Systèmes', 'ID115', 0, 0, 0, 'S1', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(85, 'Structure de données et algorithmique avancée', 'Génie Civil,Structures', 'ID116', 0, 0, 0, 'S1', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(86, 'Anglais', 'Langues,Anglais', 'ID117.1', 0, 0, 0, 'S1', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(87, 'Français', 'Langues,Français', 'ID117.2', 0, 0, 0, 'S1', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(88, 'Programmation Python Bases du Web', 'Informatique,Programmation,Développement Web', 'ID126', 0, 0, 0, 'S2', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(89, 'Data mining', 'Informatique,Data Science', 'ID127', 0, 0, 0, 'S2', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(90, 'Statistique en grande dimension', 'Général', 'ID125', 0, 0, 0, 'S2', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(91, 'Programmation orientée objet java', 'Informatique,Programmation', 'ID121', 0, 0, 0, 'S2', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(92, 'Administration et optimisation des bases de données', 'Général', 'ID122', 0, 0, 0, 'S2', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(93, 'Communication Professionnelle II : Anglais', 'Communication,Langues,Anglais', 'ID123.1', 0, 0, 0, 'S2', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(94, 'Communication Professionnelle II : Espagnol', 'Communication', 'ID123.2', 0, 0, 0, 'S2', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(95, 'Entreprenariat -I-', 'Informatique,IA', 'ID124', 0, 0, 0, 'S2', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(96, 'Inteligence Artificielle I: Maching Learning', 'Général', 'ID31', 24, 10, 24, 'S3', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(97, 'Modélisation stochastique', 'Général', 'ID32.1', 13, 10, 7, 'S3', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(98, 'Technique Mathématiques d\'Optimisation', 'Mathématiques', 'ID32.2', 13, 10, 7, 'S3', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(99, 'Architecture Logicielle et UML', 'Général', 'ID33', 24, 10, 24, 'S3', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(100, 'Fondements du Big Data', 'Informatique,Data Science', 'ID34', 24, 10, 24, 'S3', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(101, 'Français', 'Langues,Français', 'ID35.1', 12, 10, 0, 'S3', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(102, 'Anglais', 'Langues,Anglais', 'ID35.2', 12, 10, 0, 'S3', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(103, 'SoftSkills', 'Général', 'ID35.3', 10, 10, 0, 'S3', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(104, 'Bases de données Avancées', 'Général', 'ID36', 24, 10, 24, 'S3', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(105, 'Big Data Avancée', 'Informatique,Data Science', 'ID41', 24, 10, 24, 'S4', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(106, 'Inelligence artificielle-II- Deep Learning', 'Général', 'ID42', 24, 8, 32, 'S4', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(107, 'Data Werhaus et Data Lake', 'Informatique,Data Science', 'ID43', 21, 0, 21, 'S4', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(108, 'Applicataions Web avancées avec Java et spring', 'Informatique,Développement Web,Programmation', 'ID44', 21, 16, 27, 'S4', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(109, 'TAL', 'Général', 'ID45', 26, 4, 34, 'S4', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(110, 'Entreprenariat-II', 'Informatique,IA', 'ID46', 27, 15, 22, 'S4', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(111, 'Big Data Visualisation & Cloud Computing', 'Informatique,Data Science', 'ID51', 0, 0, 0, 'S5', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(112, 'Business inteligence & Veille Stratégique', 'Général', 'ID510', 0, 0, 0, 'S5', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(113, 'Entreprenariat-3-', 'Informatique,IA', 'ID511', 0, 0, 0, 'S5', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(114, 'Social Network Mining', 'Informatique,IA', 'ID512', 0, 0, 0, 'S5', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(115, 'Web Marketing et CRM', 'Informatique,Développement Web', 'ID513', 0, 0, 0, 'S5', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(116, 'Transformation Digital', 'Général', 'ID514', 0, 0, 0, 'S5', 1, NULL, 2, 2, 3, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(117, 'Algèbre 1', 'Mathématiques,Algèbre', 'AP113', 26, 26, 0, 'S1', 1, '[\"TD\"]', 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-12 00:15:59'),
(118, 'Analyse 1', 'Mathématiques,Analyse', 'AP111', 26, 26, 0, 'S1', 0, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 02:20:52'),
(119, 'Initiation à l\'informatique', 'Informatique,Programmation,Réseaux,IA', 'AP115', 26, 26, 0, 'S1', 0, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(120, 'Langues Etrangères (Anglais)', 'Langues,Anglais', 'AP116.1', 7, 15, 0, 'S1', 0, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 02:30:28'),
(121, 'Langues Etrangères (Français)', 'Langues,Français', 'AP116.2', 7, 15, 0, 'S1', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(122, 'Méthodologie de travail universitaire', 'Général', 'AP117', 28, 15, 0, 'S1', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(123, 'Algèbre 2', 'Mathématiques,Algèbre', 'AP126', 26, 26, 0, 'S2', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(124, 'Analyse 2', 'Mathématiques,Analyse', 'AP125', 26, 26, 0, 'S2', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(125, 'Programmation C', 'Informatique,Programmation', 'AP123', 26, 0, 26, 'S2', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(126, 'Langues Etrangères (Anglais / Français)', 'Langues,Anglais,Français', 'AP124', 7, 15, 0, 'S2', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(127, 'Culture digitale', 'Général', 'AP127', 10, 0, 30, 'S2', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(128, 'Algèbre 3 : Algèbre Quadratique', 'Mathématiques,Algèbre', 'AP31', 32, 32, 0, 'S3', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(129, 'Analyse 3 : Fonctions de Plusieurs Variables (FPV)', 'Informatique,IA,Mathématiques,Analyse', 'AP32', 32, 32, 0, 'S3', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(130, 'Informatique 2 : Programmation en C', 'Informatique,Programmation,Réseaux', 'AP35', 32, 32, 0, 'S3', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(131, 'Langues et Communication 3 -FR', 'Communication,Langues', 'AP36.1', 14, 8, 0, 'S3', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(132, 'Langues et Communication 3 -Ang', 'Communication,Langues', 'AP36.2', 14, 8, 0, 'S3', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(133, 'Mathématiques Appliquées- Proba et stat descr', 'Mathématiques', 'AP41A', 16, 16, 0, 'S4', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(134, 'Mathématiques Appliquées- Analyse Numerique', 'Mathématiques,Analyse', 'AP41B', 16, 16, 0, 'S4', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(135, 'Analyse 4 : Intégrales et Formes Différentielles', 'Mathématiques,Analyse', 'AP42', 32, 32, 0, 'S4', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(136, 'Informatique 3 : Outils Informatique', 'Informatique,Programmation,Réseaux', 'AP45', 32, 32, 0, 'S4', 1, NULL, 2, 2, 4, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(137, 'Mathématiques pour l\'ingenieur', 'Mathématiques', 'GC114', 0, 0, 0, 'S1', 1, NULL, 2, 2, 5, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(138, 'Langues etrangeres - Anglais', 'Langues,Anglais', 'M116GC1', 0, 0, 0, 'S1', 1, NULL, 2, 2, 5, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(139, 'Langues et Communication3', 'Communication,Langues', 'X09', 22, 0, 0, 'S3', 1, NULL, 2, 2, 5, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(140, 'Modélisations Numériques', 'Général', 'MOD_NUM', 10, 8, 0, 'S4', 1, NULL, 2, 2, 5, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(141, 'Projet', 'Projet', 'C17', 0, 0, 15, 'S4', 1, NULL, 2, 2, 5, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(142, 'Technique de communication 2', 'Communication', 'M9', 22, 0, 0, 'S3', 1, NULL, 2, 2, 6, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(143, 'Mathématiques pour l\'ingenieur', 'Mathématiques', 'M115GEER1', 26, 18, 4, 'S1', 1, NULL, 2, 2, 6, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(144, 'Anglais 1', 'Langues,Anglais', 'M116GEER1', 0, 0, 0, 'S1', 1, NULL, 2, 2, 6, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(145, 'Modélisation numérique 1', 'Général', 'M11', 24, 12, 8, 'S1', 1, NULL, 2, 2, 6, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(146, 'Modélisation numérique 2', 'Général', 'M18', 24, 12, 8, 'S2', 1, NULL, 2, 2, 6, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(147, 'Communication et langues étrangères', 'Communication,Langues', 'M235', 22, 8, 0, 'S3', 1, NULL, 2, 2, 7, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(148, 'Analyses des données et tests statistiques', 'Mathématiques,Analyse,Statistiques', 'M243A', 16, 8, 6, 'S4', 1, NULL, 2, 2, 7, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(149, 'Communication 2', 'Communication', 'M37', 12, 6, 0, 'S3', 1, NULL, 2, 2, 8, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(150, 'Modélisation numérique & Simulation', 'Général', 'M31GM', 16, 8, 0, 'S3', 1, NULL, 2, 2, 8, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(151, 'Technique de communication 3', 'Communication', 'M17', 22, 0, 0, 'S4', 1, NULL, 2, 2, 8, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(152, 'Mathématiques pour l\'ingenieur', 'Mathématiques', 'MGM', 26, 16, 6, 'S1', 1, NULL, 2, 2, 8, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25'),
(153, 'Recherche opérationnelle', 'Général', 'M44GM', 28, 18, 0, 'S1', 1, NULL, 2, 2, 8, 2, NULL, '2025-06-10 00:29:25', '2025-06-10 00:29:25');

--
-- Index pour les tables déchargées
--

--
-- Index pour la table `unites_enseignement`
--
ALTER TABLE `unites_enseignement`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`),
  ADD KEY `filiere_id` (`filiere_id`),
  ADD KEY `departement_id` (`departement_id`),
  ADD KEY `responsable_id` (`responsable_id`);

--
-- AUTO_INCREMENT pour les tables déchargées
--

--
-- AUTO_INCREMENT pour la table `unites_enseignement`
--
ALTER TABLE `unites_enseignement`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=154;

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `unites_enseignement`
--
ALTER TABLE `unites_enseignement`
  ADD CONSTRAINT `unites_enseignement_ibfk_1` FOREIGN KEY (`filiere_id`) REFERENCES `filieres` (`id`),
  ADD CONSTRAINT `unites_enseignement_ibfk_2` FOREIGN KEY (`departement_id`) REFERENCES `departements` (`id`),
  ADD CONSTRAINT `unites_enseignement_ibfk_3` FOREIGN KEY (`responsable_id`) REFERENCES `users` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
